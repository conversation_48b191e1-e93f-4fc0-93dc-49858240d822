import 'dart:async';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/models/user_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab2Venue/tab_venue_logic.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:shoot_z/utils/utils.dart';
import '../../generated/l10n.dart';
import '../../inappwebview/router.dart';
import '../../routes/app.dart';
import '../../routes/route.dart';
import '../../widgets/privacy_dialog.dart';
import 'encrypt.dart';
import 'state.dart';

class LoginLogic extends GetxController {
  final LoginState state = LoginState();
  static final List<String> areaCodes = ['+110', '+86', '+91', '+44', '+55'];

//  什么时候调用 onClose：
//  •	控制器的依赖被释放时触发，例如：
//  •	页面销毁且没有其他地方依赖该控制器。
//  •	显式调用 Get.delete 删除控制器。
  @override
  void onClose() {
    // 清理定时器
    state.countdownTimer?.cancel();
    super.onClose();
  }

  @override
  void onReady() async {
    if (UserManager.instance.showPrivacyDialog.value) {
      Get.dialog(const PrivacyDialog());
    }
    super.onReady();
  }

  void didAgree() async {
    onKeyDismiss();
    state.isAgree.value = !state.isAgree.value;
  }

  void didUserPolicy() async {
    onKeyDismiss();

    WebviewRouter router = WebviewRouter(
        url: ApiUrl.userPolicy,
        showNavigationBar: true,
        needBaseHttp: false,
        title: S.current.user_policy_line);
    AppPage.to(Routes.webview, arguments: router);
  }

  void didPrivacyPolicy() {
    onKeyDismiss();

    WebviewRouter router = WebviewRouter(
        url: ApiUrl.privacyPolicy,
        showNavigationBar: true,
        needBaseHttp: false,
        title: S.current.privacy_policy);
    AppPage.to(Routes.webview, arguments: router);
  }

  /// 开始倒计时
  void startCountdown() {
    state.countdown.value = 60;
    state.isCountdownActive.value = true;

    state.countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.countdown.value > 0) {
        state.countdown.value--;
      } else {
        timer.cancel();
        state.isCountdownActive.value = false;
      }
    });
  }

  void showAgreeSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colours.color191921,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      builder: (context) {
        return Wrap(
          children: [
            Padding(
              padding: EdgeInsets.only(
                  top: 25.w,
                  left: 25.w,
                  right: 25.w,
                  bottom: MediaQuery.of(context).padding.bottom),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child: Text(S.current.user_policy_and_privacy_policy,
                        style: TextStyles.titleMedium18
                            .copyWith(fontWeight: AppFontWeight.semiBold())),
                  ),
                  SizedBox(
                    height: 22.w,
                  ),
                  Text.rich(TextSpan(children: [
                    TextSpan(
                        text: S.current.read_and_agree,
                        style: TextStyles.display12),
                    TextSpan(
                      text: '《${S.current.user_policy}》',
                      style: TextStyles.display12.copyWith(
                        color: Colors.white,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () => didUserPolicy(),
                    ),
                    TextSpan(text: S.current.and, style: TextStyles.display12),
                    TextSpan(
                      text: '《${S.current.privacy_policy}》',
                      style: TextStyles.display12.copyWith(
                        color: Colors.white,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () => didPrivacyPolicy(),
                    ),
                  ])),
                  SizedBox(height: 4.w),
                  Text(
                    S.current.protocol_tips,
                    style: TextStyles.display12,
                  ),
                  // const Expanded(child: SizedBox()),
                  SizedBox(
                    height: 28.w,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      WxButton(
                        text: S.current.disagree,
                        textStyle: TextStyles.display15,
                        height: 46.w,
                        width: 154.w,
                        backgroundColor: Colours.color2C2C39,
                        borderRadius: BorderRadius.circular(23.w),
                        onPressed: () => AppPage.back(),
                      ),
                      WxButton(
                        text: S.current.agree_and_log_in,
                        textStyle: TextStyles.display15,
                        height: 46.w,
                        width: 154.w,
                        borderRadius: BorderRadius.circular(23.w),
                        linearGradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                        onPressed: () {
                          didAgree();
                          AppPage.back();
                          getCode(context);
                        },
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                ],
              ),
            )
          ],
        );
      },
    );
  }

  void getCode(BuildContext context) async {
    if (state.phoneController.text.isEmpty ||
        !Utils.isValidPhoneNumber(state.phoneController.text)) {
      WxLoading.showToast(S.current.mobile_number_hint);
      return;
    }
    onKeyDismiss();
    if (!state.isAgree.value) {
      showAgreeSheet(context);
      return;
    }
    WxLoading.show();
    final phone = PhoneEncryption.encryptPhone(state.phoneController.text);
    var data = {'phone': phone, 'region': state.areaCode.value};
    var res = await Api().post(ApiUrl.getCode, data: data);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      // 启动一分钟倒计时
      startCountdown();

      // AppPage.to(Routes.loginCode,
      //     arguments: {...data, 'original': state.phoneController.text});
    } else {
      WxLoading.showToast(res.message);
    }
  }

  void login() async {
    if (state.codeController.text.length != 4) {
      WxLoading.showToast('请输入有效的验证码');
      return;
    }
    onKeyDismiss();
    WxLoading.show();
    final phone = PhoneEncryption.encryptPhone(state.phoneController.text);
    var res = await Api().post(ApiUrl.login, data: {
      'phone': phone,
      'region': state.areaCode.value,
      'original': state.phoneController.text,
      'code': state.codeController.text
    });
    if (!res.isSuccessful()) {
      WxLoading.showToast(res.message);
      return;
    }
    UserModel userModel = UserModel.fromJson(res.data);
    WxEnv.instance.setToken(userModel.token, userModel.refreshToken);
    await UserManager.instance.pullUserInfo();
    WxLoading.dismiss();
    UserManager.instance.login(userModel);
    if (userModel.isFirstRegister ?? false) {
      AppPage.to(Routes.inputInviteCodePage);
    } else {
      final tabVenue = Get.isRegistered<TabVenueLogic>();
      if (tabVenue) {
        // AppPage.back(page:Routes.tab);
        AppPage.back(result: true);
      } else {
        AppPage.resetRootPageName(Routes.tab);
      }
    }
  }
  // void showCustomDialog(BuildContext context) {
  //   showGeneralDialog(
  //     context: context,
  //     barrierDismissible: true,
  //     barrierLabel: '',
  //     transitionDuration: Duration(milliseconds: 300),
  //     pageBuilder: (context, animation1, animation2) {
  //       return Align(
  //         alignment: Alignment.bottomCenter,
  //         child: Container(
  //           height: 200,
  //           width: double.infinity,
  //           padding: EdgeInsets.all(16),
  //           decoration: BoxDecoration(
  //             color: Colors.white,
  //             borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
  //           ),
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Text("Custom Dialog",
  //                   style:
  //                       TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
  //               SizedBox(height: 10),
  //               Text("This is a custom bottom dialog."),
  //               SizedBox(height: 20),
  //               ElevatedButton(
  //                 onPressed: () => AppPage.back(),//Navigator.pop(context),
  //                 child: Text("Close"),
  //               ),
  //             ],
  //           ),
  //         ),
  //       );
  //     },
  //     transitionBuilder: (context, animation1, animation2, child) {
  //       return SlideTransition(
  //         position: Tween<Offset>(begin: Offset(0, 1), end: Offset(0, 0))
  //             .animate(animation1),
  //         child: child,
  //       );
  //     },
  //   );
  // }
}
