import 'dart:async';

import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import '../../../network/api_url.dart';
import '../../tab2Venue/tab_venue_logic.dart';
import '../models/user_model.dart';
import '../user.dart';
import 'state.dart';

class LoginCodeLogic extends GetxController {
  final LoginCodeState state = LoginCodeState();
  late Timer _timer;
  late Map arguments;
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    arguments = Get.arguments;
    state.photo = arguments['original'];
    state.region = arguments['region'];
  }

  // 开始倒计时
  void startCountdown() {
    state.countdownTime.value = 60; // 倒计时初始时间为 60 秒

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.countdownTime.value > 0) {
        state.countdownTime.value--;
      } else {
        _timer.cancel(); // 倒计时结束
      }
    });
  }

  void login() async {
    if (state.codeController.text.length != 4) {
      WxLoading.showToast('请输入有效的验证码');
      return;
    }
    onKeyDismiss();
    WxLoading.show();
    var res = await Api().post(ApiUrl.login,
        data: {...arguments, 'code': state.codeController.text});
    if (!res.isSuccessful()) {
      WxLoading.showToast(res.message);
      return;
    }
    UserModel userModel = UserModel.fromJson(res.data);
    WxEnv.instance.setToken(userModel.token, userModel.refreshToken);
    await UserManager.instance.pullUserInfo();
    WxLoading.dismiss();
    UserManager.instance.login(userModel);
    if (userModel.isFirstRegister ?? false) {
      AppPage.to(Routes.inputInviteCodePage);
    } else {
      final TabVenue = Get.isRegistered<TabVenueLogic>();
      if (TabVenue) {
        // AppPage.back(page:Routes.tab);
        AppPage.back();
        AppPage.back(result: true);
      } else {
        AppPage.resetRootPageName(Routes.tab);
      }
    }
  }

  Future<void> requestCode() async {
    if (state.countdownTime.value > 0) {
      return;
    }
    WxLoading.show();
    var res = await Api().post(ApiUrl.getCode, data: arguments);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      startCountdown(); // 请求成功后开始倒计时
    } else {
      WxLoading.showToast(res.message);
    }
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
    _timer.cancel(); // 释放资源
  }
}
