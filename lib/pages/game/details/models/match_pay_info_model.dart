class MatchPayInfoModelTeams {
/*
{
  "joined": true,
  "locked": 0,
  "logo": "string",
  "paid": true,
  "teamId": "0",
  "teamName": "string"
} 
*/

  bool? joined;
  int? locked;
  String? logo;
  bool? paid;
  String? teamId;
  String? teamName;

  MatchPayInfoModelTeams({
    this.joined,
    this.locked,
    this.logo,
    this.paid,
    this.teamId,
    this.teamName,
  });
  MatchPayInfoModelTeams.fromJson(Map<String, dynamic> json) {
    joined = json['joined'];
    locked = json['locked']?.toInt();
    logo = json['logo']?.toString();
    paid = json['paid'];
    teamId = json['teamId']?.toString();
    teamName = json['teamName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['joined'] = joined;
    data['locked'] = locked;
    data['logo'] = logo;
    data['paid'] = paid;
    data['teamId'] = teamId;
    data['teamName'] = teamName;
    return data;
  }
}

class MatchPayInfoModelLockedPlayers {
/*
{
  "bound": true,
  "number": "string",
  "photo": "string",
  "playerAvatar": "string",
  "playerId": "0",
  "playerName": "string",
  "teamId": "0",
  "locked": "0"
} 
*/

  bool? bound;
  String? number;
  String? photo;
  String? playerAvatar;
  String? playerId;
  String? playerName;
  String? teamId;
  String? locked;

  MatchPayInfoModelLockedPlayers({
    this.bound,
    this.number,
    this.photo,
    this.playerAvatar,
    this.playerId,
    this.playerName,
    this.teamId,
    this.locked,
  });
  MatchPayInfoModelLockedPlayers.fromJson(Map<String, dynamic> json) {
    bound = json['bound'];
    number = json['number']?.toString();
    photo = json['photo']?.toString();
    playerAvatar = json['playerAvatar']?.toString();
    playerId = json['playerId']?.toString();
    playerName = json['playerName']?.toString();
    teamId = json['teamId']?.toString();
    locked = json['locked']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['bound'] = bound;
    data['number'] = number;
    data['photo'] = photo;
    data['playerAvatar'] = playerAvatar;
    data['playerId'] = playerId;
    data['playerName'] = playerName;
    data['teamId'] = teamId;
    data['locked'] = locked;
    return data;
  }
}

class MatchPayInfoModel {
/*
{
  "arenaId": "0",
  "arenaName": "string",
  "courts": [
    "string"
  ],
  "lockedPlayers": [
    {
      "bound": true,
      "number": "string",
      "photo": "string",
      "playerAvatar": "string",
      "playerId": "0",
      "playerName": "string",
      "teamId": "0",
      "locked": "0"
    }
  ],
  "teamSetPrice": 0.02,
  "matchSetPrice": 0.04,
  "matchSet": true,
  "matchDate": "string",
  "matchDiscountPrice": 0.5,
  "matchId": "0",
  "matchPrice": 0.1,
  "matchTime": "string",
  "playerDiscountPrice": 0.3,
  "playerPrice": 0.1,
  "status": 0,
  "teamDiscountPrice": 0.1,
  "teamPrice": 0.3,
  "teams": [
    {
      "joined": true,
      "locked": 0,
      "logo": "string",
      "paid": true,
      "teamId": "0",
      "teamName": "string"
    }
  ],
  "unlockedPlayers": [
    {
      "bound": true,
      "number": "string",
      "photo": "string",
      "playerAvatar": "string",
      "playerId": "0",
      "playerName": "string",
      "teamId": "0",
      "locked": "0"
    }
  ],
  "validCouponCount": 0,
  "boundMatchPlayerId": "0",
  "week": "string"
} 
*/

  String? arenaId;
  String? arenaName;
  List<String?>? courts;
  List<MatchPayInfoModelLockedPlayers?>? lockedPlayers;
  double? teamSetPrice;
  double? matchSetPrice;
  bool? matchSet;
  String? matchDate;
  double? matchDiscountPrice;
  String? matchId;
  double? matchPrice;
  String? matchTime;
  double? playerDiscountPrice;
  double? playerPrice;
  int? status;
  double? teamDiscountPrice;
  double? teamPrice;
  List<MatchPayInfoModelTeams?>? teams;
  List<MatchPayInfoModelLockedPlayers?>? unlockedPlayers;
  int? validCouponCount;
  String? boundMatchPlayerId;
  String? week;

  MatchPayInfoModel({
    this.arenaId,
    this.arenaName,
    this.courts,
    this.lockedPlayers,
    this.teamSetPrice,
    this.matchSetPrice,
    this.matchSet,
    this.matchDate,
    this.matchDiscountPrice,
    this.matchId,
    this.matchPrice,
    this.matchTime,
    this.playerDiscountPrice,
    this.playerPrice,
    this.status,
    this.teamDiscountPrice,
    this.teamPrice,
    this.teams,
    this.unlockedPlayers,
    this.validCouponCount,
    this.boundMatchPlayerId,
    this.week,
  });
  MatchPayInfoModel.fromJson(Map<String, dynamic> json) {
    arenaId = json['arenaId']?.toString();
    arenaName = json['arenaName']?.toString();
    if (json['courts'] != null) {
      final v = json['courts'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      courts = arr0;
    }
    if (json['lockedPlayers'] != null) {
      final v = json['lockedPlayers'];
      final arr0 = <MatchPayInfoModelLockedPlayers>[];
      v.forEach((v) {
        arr0.add(MatchPayInfoModelLockedPlayers.fromJson(v));
      });
      lockedPlayers = arr0;
    }
    teamSetPrice = json['teamSetPrice']?.toDouble();
    matchSetPrice = json['matchSetPrice']?.toDouble();
    matchSet = json['matchSet'];
    matchDate = json['matchDate']?.toString();
    matchDiscountPrice = json['matchDiscountPrice']?.toDouble();
    matchId = json['matchId']?.toString();
    matchPrice = json['matchPrice']?.toDouble();
    matchTime = json['matchTime']?.toString();
    playerDiscountPrice = json['playerDiscountPrice']?.toDouble();
    playerPrice = json['playerPrice']?.toDouble();
    status = json['status']?.toInt();
    teamDiscountPrice = json['teamDiscountPrice']?.toDouble();
    teamPrice = json['teamPrice']?.toDouble();
    if (json['teams'] != null) {
      final v = json['teams'];
      final arr0 = <MatchPayInfoModelTeams>[];
      v.forEach((v) {
        arr0.add(MatchPayInfoModelTeams.fromJson(v));
      });
      teams = arr0;
    }
    if (json['unlockedPlayers'] != null) {
      final v = json['unlockedPlayers'];
      final arr0 = <MatchPayInfoModelLockedPlayers>[];
      v.forEach((v) {
        arr0.add(MatchPayInfoModelLockedPlayers.fromJson(v));
      });
      unlockedPlayers = arr0;
    }
    validCouponCount = json['validCouponCount']?.toInt();
    boundMatchPlayerId = json['boundMatchPlayerId']?.toString();
    week = json['week']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenaId'] = arenaId;
    data['arenaName'] = arenaName;
    if (courts != null) {
      final v = courts;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['courts'] = arr0;
    }
    if (lockedPlayers != null) {
      final v = lockedPlayers;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['lockedPlayers'] = arr0;
    }
    data['teamSetPrice'] = teamSetPrice;
    data['matchSetPrice'] = matchSetPrice;
    data['matchSet'] = matchSet;
    data['matchDate'] = matchDate;
    data['matchDiscountPrice'] = matchDiscountPrice;
    data['matchId'] = matchId;
    data['matchPrice'] = matchPrice;
    data['matchTime'] = matchTime;
    data['playerDiscountPrice'] = playerDiscountPrice;
    data['playerPrice'] = playerPrice;
    data['status'] = status;
    data['teamDiscountPrice'] = teamDiscountPrice;
    data['teamPrice'] = teamPrice;
    if (teams != null) {
      final v = teams;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['teams'] = arr0;
    }
    if (unlockedPlayers != null) {
      final v = unlockedPlayers;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['unlockedPlayers'] = arr0;
    }
    data['validCouponCount'] = validCouponCount;
    data['boundMatchPlayerId'] = boundMatchPlayerId;
    data['week'] = week;
    return data;
  }
}
