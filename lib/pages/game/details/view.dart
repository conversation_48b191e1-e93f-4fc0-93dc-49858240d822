import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../gen/assets.gen.dart';
import '../../../generated/l10n.dart';
import '../../../routes/app.dart';
import '../../../widgets/vertical_dashed_line.dart';
import 'analyze_item_view.dart';
import 'logic.dart';

class GameDetailsPage extends StatelessWidget {
  GameDetailsPage({super.key});

  final logic = Get.put(GameDetailsLogic());
  final state = Get.find<GameDetailsLogic>().state;

  @override
  Widget build(BuildContext context) {
    logic.subscribe(context);
    return Container(
      color: Colours.bg_color,
      child: Stack(
        children: [
          _bodyView(context),
          _title(context),
        ],
      ),
    );
  }

  Widget _bodyView(BuildContext context) {
    return Obx(
      () => state.init.value
          ? Stack(children: [
              Column(children: [
                Expanded(
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    padding: EdgeInsets.only(
                        bottom: 10.w,
                        top: kToolbarHeight +
                            MediaQuery.of(context).padding.top),
                    controller: state.scrollController,
                    child: Column(
                      children: [
                        _topView(context),
                        Column(
                          children: [
                            _best(context),
                            _score(context),
                            _analyze(context),
                            _history(context),
                          ],
                        ).paddingSymmetric(horizontal: 15.w),
                      ],
                    ),
                  ),
                ),
                _bottomView(context),
              ]),
              // 动画显示的视图
              Obx(
                () => AnimatedPositioned(
                  duration: const Duration(milliseconds: 300),
                  top: state.showTop.value
                      ? kToolbarHeight + MediaQuery.of(context).padding.top
                      : kToolbarHeight +
                          MediaQuery.of(context).padding.top -
                          state.topViewHeight,
                  left: 0,
                  right: 0,
                  child: _animatedTopView(context),
                ),
              ),
            ])
          : buildLoad(),
    );
  }

  Widget _bottomView(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          top: 20.w,
          bottom: MediaQuery.of(context).padding.bottom > 0
              ? MediaQuery.of(context).padding.bottom
              : 34),
      color: Colours.bg_color,
      child: Column(
        children: [
          Obx(
            () => WxButton(
              text: state.allLocked.value ? '分享比赛报告' : '解锁本场',
              textStyle: TextStyles.semiBold,
              linearGradient: GradientUtils.mainGradient,
              height: 55.w,
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              borderRadius: BorderRadius.circular(27.5.w),
              onPressed: logic.unlockAll,
            ),
          ),
        ],
      ),
    );
  }

  Widget _animatedTopView(BuildContext context) {
    return Container(
      // height: state.topViewHeight,
      decoration: BoxDecoration(
        gradient: GradientUtils.mainGradient,
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 110.w,
                  child: Row(children: [
                    Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1,
                          color: Colours.white,
                        ),
                        shape: BoxShape.circle,
                        color: Colours.color7732ED,
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Text(
                      state.model.value.teams?.first?.teamName ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 13.sp,
                          color: Colours.white.withOpacity(0.9)),
                    ),
                  ]),
                ),
                Row(children: [
                  Text(
                    "${state.model.value.teams?.first?.score ?? ""}",
                    style: GoogleFonts.oswald(
                        fontWeight: AppFontWeight.medium(),
                        fontSize: 18.sp,
                        color: Colours.white),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    width: 15.w,
                  ),
                  WxAssets.images.icGameVs.image(),
                  SizedBox(
                    width: 15.w,
                  ),
                  Text(
                    "${state.model.value.teams?.last?.score ?? ""}",
                    style: GoogleFonts.oswald(
                        fontWeight: AppFontWeight.medium(),
                        fontSize: 18.sp,
                        color: Colours.white),
                    textAlign: TextAlign.center,
                  ),
                ]),
                SizedBox(
                  width: 110.w,
                  child:
                      Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                    Text(
                      state.model.value.teams?.last?.teamName ?? "",
                      style: TextStyles.regular.copyWith(
                          fontSize: 13.sp,
                          color: Colours.white.withOpacity(0.9)),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1,
                          color: Colours.white,
                        ),
                        shape: BoxShape.circle,
                        color: Colours.colorE282FF,
                      ),
                    ),
                  ]),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 12.w,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                WxButton(
                  text: '查看比赛报告',
                  textStyle: TextStyles.regular.copyWith(fontSize: 12.sp),
                  width: 155.w,
                  height: 32.w,
                  backgroundColor: Colours.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                  onPressed: () =>
                      AppPage.to(Routes.teamReportPage, arguments: {
                    'teamId': state.model.value.teams?.first?.teamId,
                    'matchId': state.model.value.matchId,
                    'notDetails': false
                  }),
                ),
                WxButton(
                  text: '查看比赛报告',
                  textStyle: TextStyles.regular.copyWith(fontSize: 12.sp),
                  width: 155.w,
                  height: 32.w,
                  backgroundColor: Colours.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                  onPressed: () =>
                      AppPage.to(Routes.teamReportPage, arguments: {
                    'teamId': state.model.value.teams?.last?.teamId,
                    'matchId': state.model.value.matchId,
                    'notDetails': false
                  }),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10.w,
          ),
        ],
      ),
    );
  }

  Widget _topView(BuildContext context) {
    final cd =
        state.model.value.courts?.map((e) => e?.courtName).toList().join('、');
    return Stack(
      children: [
        Container(
          height: 247.w,
          decoration: BoxDecoration(gradient: GradientUtils.mainGradient),
        ),
        SizedBox(
          width: Get.width,
          child: Column(
            children: [
              SizedBox(
                height: 5.w,
              ),
              Text(
                '${state.model.value.arenaName}、场地$cd',
                style: TextStyles.regular.copyWith(fontSize: 12.sp),
              ),
              SizedBox(
                height: 12.w,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 98.w,
                      child: Column(children: [
                        Container(
                          width: 38.w,
                          height: 38.w,
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: Colours.white,
                            ),
                            shape: BoxShape.circle,
                            image: DecorationImage(
                                image: (state.model.value.teams?.first?.logo) ==
                                        ""
                                    ? WxAssets.images.myTeamHead4.provider()
                                    : CachedNetworkImageProvider(
                                        state.model.value.teams?.first?.logo ??
                                            "")),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        Text(
                          state.model.value.teams?.first?.teamName ?? "",
                          style: TextStyles.regular.copyWith(
                              fontSize: 12.sp,
                              color: Colours.white.withOpacity(0.9)),
                        ),
                      ]),
                    ),
                    Column(
                      children: [
                        Row(children: [
                          SizedBox(
                            width: 54.w,
                            child: Text(
                              "${state.model.value.teams?.first?.score ?? ""}",
                              style: GoogleFonts.oswald(
                                  fontWeight: AppFontWeight.medium(),
                                  fontSize: 26.sp,
                                  color: Colours.white),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(
                            width: 14.w,
                          ),
                          WxAssets.images.icGameVs.image(),
                          SizedBox(
                            width: 14.w,
                          ),
                          SizedBox(
                            width: 54.w,
                            child: Text(
                              "${state.model.value.teams?.last?.score ?? ""}",
                              style: GoogleFonts.oswald(
                                  fontWeight: AppFontWeight.medium(),
                                  fontSize: 26.sp,
                                  color: Colours.white),
                            ),
                          ),
                        ]),
                        SizedBox(
                          height: 6.w,
                        ),
                        Text(
                          '${state.model.value.matchWeek} ${state.model.value.matchDate} ${state.model.value.matchTime}',
                          style: TextStyles.regular.copyWith(
                              fontSize: 12.sp, color: Colours.colorD5B6F8),
                        ),
                      ],
                    ),
                    SizedBox(
                      width: 98.w,
                      child: Column(children: [
                        Container(
                          width: 38.w,
                          height: 38.w,
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: Colours.white,
                            ),
                            shape: BoxShape.circle,
                            image: DecorationImage(
                                image: (state.model.value.teams?.last?.logo) ==
                                        ""
                                    ? WxAssets.images.myTeamHead4.provider()
                                    : CachedNetworkImageProvider(
                                        state.model.value.teams?.last?.logo ??
                                            "")),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        Text(
                          state.model.value.teams?.last?.teamName ?? "",
                          style: TextStyles.regular.copyWith(
                              fontSize: 12.sp,
                              color: Colours.white.withOpacity(0.9)),
                        ),
                      ]),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 14.w,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    WxButton(
                      text: '查看比赛报告',
                      textStyle: TextStyles.semiBold.copyWith(fontSize: 12.sp),
                      width: 155.w,
                      height: 38.w,
                      backgroundColor: Colours.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                      onPressed: () =>
                          AppPage.to(Routes.teamReportPage, arguments: {
                        'teamId': state.model.value.teams?.first?.teamId,
                        'matchId': state.model.value.matchId,
                        'notDetails': false
                      }),
                    ),
                    WxButton(
                      text: '查看比赛报告',
                      textStyle: TextStyles.semiBold.copyWith(fontSize: 12.sp),
                      width: 155.w,
                      height: 38.w,
                      backgroundColor: Colours.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                      onPressed: () =>
                          AppPage.to(Routes.teamReportPage, arguments: {
                        'teamId': state.model.value.teams?.last?.teamId,
                        'matchId': state.model.value.matchId,
                        'notDetails': false
                      }),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 15.w,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: AspectRatio(
                    aspectRatio: 345 / 194, // 宽高比
                    child: Stack(
                      children: [
                        VideoView(controller: state.videoController),
                        // Obx(
                        //   () {
                        //     final videoPath = state.matchVideoPath.value;
                        //     if (videoPath.isEmpty) return const SizedBox();

                        //     return Container(
                        //       color: Colors.black,
                        //       child: state.chewieController.value == null
                        //         ? (() {
                        //             logic.initVideo();
                        //             return const Center(
                        //               key: ValueKey('loading'),
                        //               child: CircularProgressIndicator(),
                        //             );
                        //           })()
                        //         : Chewie(
                        //             key: const ValueKey('video'),
                        //             controller: state.chewieController.value!,
                        //           ),
                        //     );
                        //   },
                        // ),
                        Obx(() {
                          return Visibility(
                            visible: state.matchVideoPath.value.isEmpty,
                            child: Container(
                              color: Colours.color191921,
                              alignment: Alignment.center,
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 36.w,
                                  ),
                                  WxAssets.images.icVideoScz
                                      .image(width: 80.w, fit: BoxFit.fill),
                                  SizedBox(
                                    height: 22.w,
                                  ),
                                  Text(
                                    S.current.highlights_generate,
                                    style: TextStyles.regular.copyWith(
                                        fontSize: 12.sp,
                                        color: Colours.color5C5C6E),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _best(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 25.w, bottom: 20.w),
          child: Text(
            '本场最佳',
            style: TextStyles.regular.copyWith(fontSize: 16.sp),
          ),
        ),
        Container(
          padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3), // 阴影颜色
                offset: const Offset(0, 4), // 阴影偏移 (水平, 垂直)
                blurRadius: 10, // 阴影模糊半径
                spreadRadius: 1, // 阴影扩散半径
              ),
            ],
          ),
          child: ListView.builder(
              physics: const ClampingScrollPhysics(),
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: 3,
              itemBuilder: (context, index) {
                return Obx(() => _bestListItem(context, index));
              }),
        ),
      ],
    );
  }

  Widget _bestListItem(BuildContext context, int index) {
    // final leftBest = logic.state.model.value.teams?.first!;
    // final rightBest = logic.state.model.value.teams?.last!;
    // leftBest.
    // leftBest?.scoreKing;
    // leftBest?.scoreKing;
    // leftBest?.scoreKing;
    // final left = index == 0
    //     ? leftBest?.scoreKing
    //     : (index == 1 ? leftBest?.reboundKing : leftBest?.assistKing);
    // final right = index == 0
    //     ? rightBest?.scoreKing
    //     : (index == 1 ? rightBest?.reboundKing : rightBest?.assistKing);
    // final leftS = left?.score ?? 0;
    // final rightS = right?.score ?? 0;
    // final text = index == 0 ? '得分' : (index == 1 ? '篮板' : '助攻');

    final leftBest = state.leftTeam.value;
    final rightBest = state.rightTeam.value;
    var left = index == 0
        ? leftBest.scoreKing
        : index == 1
            ? leftBest.reboundKing
            : leftBest.assistKing;
    var right = index == 0
        ? rightBest.scoreKing
        : index == 1
            ? rightBest.reboundKing
            : rightBest.assistKing;

    final leftS = index == 0
        ? int.parse(
            left?.score == "" || left?.score == null ? '0' : left?.score ?? '0')
        : (index == 1 ? left?.rebound ?? 0 : left?.assist ?? 0);
    final rightS = index == 0
        ? int.parse(right?.score == "" || right?.score == null
            ? '0'
            : right?.score ?? '0')
        : (index == 1 ? right?.rebound ?? 0 : right?.assist ?? 0);

    final text = index == 0 ? '得分' : (index == 1 ? '篮板' : '助攻');
    return Row(
      children: [
        Expanded(
            child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => logic.unlockPlayer(
              state.model.value.teams?.first?.teamId ?? "",
              left?.playerId ?? "",
              left?.locked == 1),
          child: Row(
            children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Stack(children: [
                    MyImage(
                      left?.photo ?? "",
                      width: 50.w,
                      height: 60.w,
                      isAssetImage: false,
                      radius: 4.r,
                    ),
                    Visibility(
                      visible: left?.locked == 1,
                      child: Positioned.fill(
                          child: Container(
                        color: Colors.black.withOpacity(0.5),
                        alignment: Alignment.center,
                        child: WxAssets.images.icGameLock
                            .image(width: 18.w, fit: BoxFit.fill),
                      )),
                    ),
                  ])),
              SizedBox(
                width: 10.w,
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    left?.locked == 1 ? '**' : leftS.toString(),
                    style: GoogleFonts.oswald(
                        fontSize: 20.sp,
                        fontWeight: AppFontWeight.medium(),
                        color: Colours.white,
                        height: 1),
                  ),
                  SizedBox(
                    height: 18.w,
                  ),
                  Text(
                    '#${left?.number ?? ""}',
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  )
                ],
              ),
            ],
          ),
        )),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  width: 11.w,
                  height: (leftS >= rightS ? 1 : leftS / rightS) * 32.w,
                  decoration: BoxDecoration(
                    color: leftS < rightS
                        ? Colours.color2F2F3B
                        : Colours.color7732ED,
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(2)),
                  ),
                ),
                SizedBox(
                  width: 6.w,
                ),
                Container(
                  width: 11.w,
                  height: (leftS > rightS ? rightS / leftS : 1) * 32.w,
                  decoration: BoxDecoration(
                    color: rightS < leftS
                        ? Colours.color2F2F3B
                        : Colours.colorE282FF,
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(2)),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 10.w,
            ),
            Text(
              text,
              style: TextStyles.regular.copyWith(fontSize: 12.sp),
            ),
          ],
        ),
        Expanded(
            child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => logic.unlockPlayer(
              state.model.value.teams?.last?.teamId ?? "",
              right?.playerId ?? "",
              right?.locked == 1),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    right?.locked == 1 ? '**' : rightS.toString(),
                    style: GoogleFonts.oswald(
                        fontSize: 20.sp,
                        fontWeight: AppFontWeight.medium(),
                        color: Colours.white,
                        height: 1),
                  ),
                  SizedBox(
                    height: 18.w,
                  ),
                  Text(
                    '#${right?.number ?? ""}',
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  )
                ],
              ),
              SizedBox(
                width: 10.w,
              ),
              ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Stack(children: [
                    MyImage(
                      right?.photo ?? "",
                      width: 50.w,
                      height: 60.w,
                      isAssetImage: false,
                      radius: 4.r,
                    ),
                    Visibility(
                      visible: right?.locked == 1,
                      child: Positioned.fill(
                          child: Container(
                        color: Colors.black.withOpacity(0.5),
                        alignment: Alignment.center,
                        child: WxAssets.images.icGameLock
                            .image(width: 18.w, fit: BoxFit.fill),
                      )),
                    ),
                  ])),
            ],
          ),
        )),
      ],
    ).paddingOnly(bottom: 20.w);
  }

  Widget _score(BuildContext context) {
    final hide =
        state.sectionScoreList == null || state.sectionScoreList!.length != 2;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 25.w, bottom: 20.w),
          child: Text(
            '比分走势',
            style: TextStyles.regular.copyWith(fontSize: 16.sp),
          ),
        ),
        Visibility(
          visible: !hide,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 16.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListView.builder(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: 3,
                itemBuilder: (context, index) {
                  return _scoreListItem(context, index);
                }),
          ).marginOnly(bottom: 13.w),
        ),
        Container(
          width: 345.w,
          height: 180.w,
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(12),
          ),
          child: _lineChart(context),
        ),
      ],
    );
  }

  Widget _lineChart(BuildContext context) {
    final left = (state.model.value.teams?.first?.maxOffsetScore ?? 0) <= 0
        ? '无领先时刻'
        : '最高领先${state.model.value.teams?.first?.maxOffsetScore ?? 0}分';
    final right = (state.model.value.teams?.last?.maxOffsetScore ?? 0) <= 0
        ? '无领先时刻'
        : '最高领先${state.model.value.teams?.last?.maxOffsetScore}分';
    return Stack(children: [
      LayoutBuilder(
        builder: (context, constraints) => Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            VerticalDashedLine(
              height: constraints.maxHeight, // 高度
              dashHeight: 3.w, // 虚线段高度
              dashSpacing: 3.w, // 虚线间距
              color: Colours.color282835, // 虚线颜色
            ),
            VerticalDashedLine(
              height: constraints.maxHeight, // 高度
              dashHeight: 3.w, // 虚线段高度
              dashSpacing: 3.w, // 虚线间距
              color: Colours.color282835, // 虚线颜色
            ),
            VerticalDashedLine(
              height: constraints.maxHeight, // 高度
              dashHeight: 3.w, // 虚线段高度
              dashSpacing: 3.w, // 虚线间距
              color: Colours.color282835, // 虚线颜色
            ),
          ],
        ).paddingSymmetric(horizontal: 92.w),
      ),
      LineChart(logic.lineChartData)
          .paddingOnly(left: 12.w, right: 12.w, top: 32.w, bottom: 12.w),
      Positioned(
          left: 15.w,
          top: 18.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                        color: Colours.color7732ED,
                        borderRadius: BorderRadius.circular(2.w)),
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                  Text(
                    '${state.model.value.teams?.first?.teamName ?? ""}（$left）',
                    style: TextStyles.regular
                        .copyWith(fontSize: 10.sp, color: Colours.color5C5C6E),
                  )
                ],
              ),
              SizedBox(
                height: 10.w,
              ),
              Row(
                children: [
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                        color: Colours.colorE282FF,
                        borderRadius: BorderRadius.circular(2.w)),
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                  Text(
                    '${state.model.value.teams?.last?.teamName ?? ""}（$right）',
                    style: TextStyles.regular
                        .copyWith(fontSize: 10.sp, color: Colours.color5C5C6E),
                  )
                ],
              ),
            ],
          )),
    ]);
  }

  Widget _scoreListItem(BuildContext context, int index) {
    final one = state.sectionScoreList!.first;
    final two = state.sectionScoreList!.last;
    final color = index == 0 ? Colours.color5C5C6E : Colours.white;
    final text2 =
        index == 0 ? "Q1" : (index == 1 ? one.secScore1 : two.secScore1);
    final text3 =
        index == 0 ? "Q2" : (index == 1 ? one.secScore2 : two.secScore2);
    final text4 =
        index == 0 ? "Q3" : (index == 1 ? one.secScore3 : two.secScore3);
    final text5 =
        index == 0 ? "Q4" : (index == 1 ? one.secScore4 : two.secScore4);
    final text6 =
        index == 0 ? "总分" : (index == 1 ? one.totalScore : two.totalScore);
    final url = index == 1 ? one.teamLogo : two.teamLogo;
    final widget = index == 0
        ? _scoreText('球队', color)
        : Center(
            child: ClipRRect(
                borderRadius: BorderRadius.circular(9),
                child: CachedNetworkImage(
                    imageUrl: url, width: 18.w, fit: BoxFit.cover)));
    return Row(
      children: [
        Expanded(child: widget),
        Expanded(child: _scoreText(text2.toString(), color)),
        Expanded(child: _scoreText(text3.toString(), color)),
        Expanded(child: _scoreText(text4.toString(), color)),
        Expanded(child: _scoreText(text5.toString(), color)),
        Expanded(child: _scoreText(text6.toString(), color)),
      ],
    ).paddingOnly(bottom: index < 2 ? 20.w : 0);
  }

  Widget _scoreText(String text, Color color) {
    return Text(
      text,
      style: TextStyles.regular.copyWith(fontSize: 12.sp, color: color),
      textAlign: TextAlign.center,
    );
  }

  Widget _analyze(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 25.w, bottom: 20.w),
          child: Text(
            '技术分析',
            style: TextStyles.regular.copyWith(fontSize: 16.sp),
          ),
        ),
        Container(
          padding:
              EdgeInsets.only(left: 20.w, right: 20.w, top: 20.w, bottom: 6.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(12),
          ),
          child: ListView.builder(
              physics: const ClampingScrollPhysics(),
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: 10,
              itemBuilder: (context, index) {
                final leftTeam =
                    state.model.value.teams?.first?.teamScoreDetail;
                final rightTeam =
                    state.model.value.teams?.last?.teamScoreDetail;
                var needCalculate = true;
                var left = 0.0;
                var right = 0.0;
                var title = '';
                switch (index) {
                  case 0:
                    title = '得分';
                    left = (leftTeam?.totalScore ?? 0).toDouble();
                    right = (rightTeam?.totalScore ?? 0).toDouble();
                  case 1:
                    title = '篮板';
                    left = (leftTeam?.reboundCount ?? 0).toDouble();
                    right = (rightTeam?.reboundCount ?? 0).toDouble();
                  case 2:
                    title = '助攻';
                    left = (leftTeam?.assistCount ?? 0).toDouble();
                    right = (rightTeam?.assistCount ?? 0).toDouble();
                  case 3:
                    title = '投篮命中率';
                    left = double.parse(leftTeam?.shootRate != "" &&
                                leftTeam?.shootRate != null
                            ? leftTeam?.shootRate ?? '0.0'
                            : '0.0') /
                        100;
                    right = double.parse(rightTeam?.shootRate != "" &&
                                rightTeam?.shootRate != null
                            ? rightTeam?.shootRate ?? '0.0'
                            : '0.0') /
                        100;
                    needCalculate = false;
                  case 4:
                    title = '三分';

                    left = (leftTeam?.threePointShootCount ?? 0).toDouble();
                    right = (rightTeam?.threePointShootCount ?? 0).toDouble();
                  case 5:
                    title = '三分命中率';
                    left = double.parse(leftTeam?.threePointShootRate != "" &&
                                leftTeam?.threePointShootRate != null
                            ? leftTeam?.threePointShootRate ?? '0.0'
                            : '0.0') /
                        100;
                    right = double.parse(rightTeam?.threePointShootRate != "" &&
                                rightTeam?.threePointShootRate != null
                            ? rightTeam?.threePointShootRate ?? '0.0'
                            : '0.0') /
                        100;

                    needCalculate = false;
                  case 6:
                    title = '前场篮板';
                    left = (leftTeam?.offensiveReboundCount ?? 0).toDouble();
                    right = (rightTeam?.offensiveReboundCount ?? 0).toDouble();
                  case 7:
                    title = '后场篮板';
                    left = (leftTeam?.defensiveReboundCount ?? 0).toDouble();
                    right = (rightTeam?.defensiveReboundCount ?? 0).toDouble();
                  case 8:
                    title = '罚球';
                    left = (leftTeam?.freeThrowShootCount ?? 0).toDouble();
                    right = (rightTeam?.freeThrowShootCount ?? 0).toDouble();
                  case 9:
                    title = '罚球命中率';

                    left = double.parse(leftTeam?.freeThrowShootRate != "" &&
                                leftTeam?.freeThrowShootRate != null
                            ? leftTeam?.freeThrowShootRate ?? '0.0'
                            : '0.0') /
                        100;
                    right = double.parse(rightTeam?.freeThrowShootRate != "" &&
                                rightTeam?.freeThrowShootRate != null
                            ? rightTeam?.freeThrowShootRate ?? '0.0'
                            : '0.0') /
                        100;

                    needCalculate = false;
                }
                return AnalyzeItemView(
                  left: left,
                  right: right,
                  title: title,
                  needCalculate: needCalculate,
                ).marginOnly(bottom: 18.w);
              }),
        ),
      ],
    );
  }

  Widget _history(BuildContext context) {
    final hide =
        state.model.value.history == null || state.model.value.history!.isEmpty;
    return Visibility(
      visible: !hide,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 25.w, bottom: 20.w),
            child: Text(
              '历史交锋',
              style: TextStyles.regular.copyWith(fontSize: 16.sp),
            ),
          ),
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ListView.builder(
                physics: const ClampingScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: state.model.value.history?.length ?? 0,
                itemBuilder: (context, index) {
                  return _historyListItem(context, index);
                }),
          ),
        ],
      ),
    );
  }

  Widget _historyListItem(BuildContext context, int index) {
    final data = state.model.value.history![index];
    final leftText = data?.winner == 0 ? '平' : (data?.winner == 1 ? '胜' : '负');
    final leftColor = data?.winner == 0
        ? Colours.color2F2F3B
        : (data?.winner == 1 ? Colours.color5E2E33 : Colours.color3B5844);
    final leftTextColor =
        data?.winner == 2 ? Colours.color5C5C6E : Colours.white;
    final rightText = data?.winner == 0 ? '平' : (data?.winner == 2 ? '胜' : '负');
    final rightColor = data?.winner == 0
        ? Colours.color2F2F3B
        : (data?.winner == 2 ? Colours.color5E2E33 : Colours.color3B5844);
    final rightTextColor =
        data?.winner == 1 ? Colours.color5C5C6E : Colours.white;
    return Row(
      children: [
        Expanded(
            child: Row(
          children: [
            Container(
              width: 20.w,
              height: 20.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: leftColor,
                borderRadius: BorderRadius.circular(4.w),
              ),
              child: Text(
                leftText,
                style: TextStyles.regular.copyWith(fontSize: 12.sp),
              ),
            ),
            SizedBox(
              width: 20.w,
            ),
            Text(
              (data?.leftScore ?? "").toString(),
              style: GoogleFonts.oswald(
                  fontSize: 18.sp,
                  color: leftTextColor,
                  fontWeight: AppFontWeight.semiBold()),
            )
          ],
        )),
        Text(
          data?.matchTime ?? "",
          style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
        ),
        Expanded(
            child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              (data?.rightScore ?? "").toString(),
              style: GoogleFonts.oswald(
                  fontSize: 18.sp,
                  color: rightTextColor,
                  fontWeight: AppFontWeight.semiBold()),
            ),
            SizedBox(
              width: 20.w,
            ),
            Container(
              width: 20.w,
              height: 20.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: rightColor,
                borderRadius: BorderRadius.circular(4.w),
              ),
              child: Text(
                rightText,
                style: TextStyles.regular.copyWith(fontSize: 12.sp),
              ),
            ),
          ],
        )),
      ],
    ).paddingOnly(
        bottom:
            index < (state.model.value.history?.length ?? 0) - 1 ? 25.w : 0);
  }

  Widget _title(BuildContext context) {
    return SizedBox(
      height: kToolbarHeight + MediaQuery.of(context).padding.top,
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(gradient: GradientUtils.mainGradient),
          ),
          Positioned(
              top: MediaQuery.of(context).padding.top,
              bottom: 0,
              left: 0,
              right: 0,
              child: Stack(children: [
                Positioned(
                  top: 0,
                  bottom: 0,
                  child: GestureDetector(
                    onTap: () => AppPage.back(),
                    child: WxAssets.images.arrowLeft.image(color: Colors.white),
                  ),
                ),
                Center(
                  child: Text(
                    S.current.competition_overview,
                    style: TextStyles.display16,
                  ),
                ),
                Positioned(
                    right: 15.w,
                    top: 0,
                    bottom: 0,
                    child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: logic.share,
                        child: Center(
                            child: WxAssets.images.share3
                                .image(width: 20.w, fit: BoxFit.fill)))),
              ])),
        ],
      ),
    );
  }
}
