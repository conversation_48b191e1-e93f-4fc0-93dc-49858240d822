import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item1/tab_home_item_view1.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item2/tab_home_item_view2.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item3/tab_home_item_view3.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item4/tab_home_item_view4.dart';
import 'package:shoot_z/pages/tab1Home/tab_home_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:ui_packages/ui_packages.dart';

///一级页面 首页
class TabHomePage extends StatelessWidget {
  TabHomePage({super.key});

  final logic = Get.put(TabHomeLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return Center(
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : Column(
                  children: [
                    SizedBox(
                      height: ScreenUtil().statusBarHeight,
                    ),
                    Expanded(
                      child: DefaultTabController(
                          length: 4,
                          child: NestedScrollView(
                            controller: logic.scrollController,
                            headerSliverBuilder: (context, innerBoxIsScrolled) {
                              return [
                                SliverToBoxAdapter(
                                  child: teamAppBar(context),
                                ),
                                // 轮播图区域
                                SliverToBoxAdapter(
                                  child: banner(),
                                ),
                                // SliverToBoxAdapter(
                                //   child: tabBarWidget(),
                                // ),
                                SliverPersistentHeader(
                                  pinned: true,
                                  delegate: _MinimalTabBarDelegate(
                                    tabBar: TabBar(
                                        controller: logic.tabController,
                                        unselectedLabelColor:
                                            Colours.color5C5C6E,
                                        unselectedLabelStyle: TextStyle(
                                            fontSize: 18.sp,
                                            color: Colours.color5C5C6E,
                                            fontWeight: FontWeight.w600),
                                        labelColor: Colours.white,
                                        labelStyle: TextStyle(
                                            fontSize: 20.sp,
                                            color: Colours.white,
                                            fontWeight: FontWeight.w600),
                                        isScrollable: true,
                                        // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                                        indicatorPadding: EdgeInsets.zero,
                                        dividerColor: Colors.transparent,
                                        dividerHeight: 0,
                                        labelPadding:
                                            const EdgeInsets.symmetric(
                                                horizontal: 4.0), // 调整标签间的间距
                                        indicatorSize:
                                            TabBarIndicatorSize.label,
                                        padding: EdgeInsets.zero,
                                        indicatorColor: Colors.transparent,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        tabAlignment: TabAlignment.start,
                                        tabs: List.generate(
                                            logic.tabNameList.length, (index) {
                                          return Obx(() {
                                            return SizedBox(
                                              width: 50.w,
                                              height: 42.w,
                                              child: Stack(
                                                alignment:
                                                    Alignment.bottomCenter,
                                                children: [
                                                  if (logic.tabbarIndex.value ==
                                                      index)
                                                    WxAssets.images.imgCheckIn2
                                                        .image(
                                                            width: 19.w,
                                                            height: 9.w),
                                                  Positioned(
                                                      bottom: 10.w,
                                                      child: Text(
                                                        logic
                                                            .tabNameList[index],
                                                        style: TextStyles
                                                            .regular
                                                            .copyWith(
                                                          fontSize: logic
                                                                      .tabbarIndex
                                                                      .value ==
                                                                  index
                                                              ? 16.sp
                                                              : 14.sp,
                                                          color: logic.tabbarIndex
                                                                      .value ==
                                                                  index
                                                              ? Colours.white
                                                              : Colours
                                                                  .color5C5C6E,
                                                        ),
                                                      )),
                                                ],
                                              ),
                                            );
                                          });
                                        })),
                                  ),
                                ),
                                //     SliverAppBar(
                                //       // 关键高度设置
                                //       toolbarHeight: 0, // 设置主高度
                                //       expandedHeight: 0, // 禁用扩展
                                //       // 禁用所有可能增加高度的部分
                                //       title: const SizedBox.shrink(), // 隐藏标题
                                //       leading: const SizedBox.shrink(), // 隐藏返回按钮
                                //       actions: const [], // 隐藏操作按钮
                                //       collapsedHeight: 0,
                                //       pinned: true,
                                //       floating: true,
                                //       snap: false,
                                //       automaticallyImplyLeading: false,
                                //       backgroundColor:
                                //           Colours.color0F0F16, //color0F0F16
                                //       bottom: PreferredSize(
                                //         preferredSize:
                                //             Size.fromHeight(40.w), // 固定高度30

                                //         child: Container(
                                //           width: double.infinity,
                                //           color: Colours.color0F0F16,
                                //           alignment: Alignment.topLeft,
                                //           padding: EdgeInsets.only(bottom: 5.w),
                                //           child: TabBar(
                                //               controller: logic.tabController,
                                //               unselectedLabelColor:
                                //                   Colours.color5C5C6E,
                                //               unselectedLabelStyle: TextStyle(
                                //                   fontSize: 18.sp,
                                //                   color: Colours.color5C5C6E,
                                //                   fontWeight: FontWeight.w600),
                                //               labelColor: Colours.white,
                                //               labelStyle: TextStyle(
                                //                   fontSize: 20.sp,
                                //                   color: Colours.white,
                                //                   fontWeight: FontWeight.w600),
                                //               isScrollable: true,
                                //               // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
                                //               indicatorPadding: EdgeInsets.zero,
                                //               dividerColor: Colors.transparent,
                                //               dividerHeight: 0,
                                //               labelPadding:
                                //                   const EdgeInsets.symmetric(
                                //                       horizontal: 4.0), // 调整标签间的间距
                                //               indicatorSize:
                                //                   TabBarIndicatorSize.label,
                                //               padding: EdgeInsets.zero,
                                //               indicatorColor: Colors.transparent,
                                //               physics:
                                //                   const NeverScrollableScrollPhysics(),
                                //               tabAlignment: TabAlignment.start,
                                //               tabs: List.generate(
                                //                   logic.tabNameList.length,
                                //                   (index) {
                                //                 return Obx(() {
                                //                   return SizedBox(
                                //                     width: 50.w,
                                //                     height: 30.w,
                                //                     child: Stack(
                                //                       alignment:
                                //                           Alignment.bottomCenter,
                                //                       children: [
                                //                         if (logic.tabbarIndex
                                //                                 .value ==
                                //                             index)
                                //                           WxAssets
                                //                               .images.imgCheckIn2
                                //                               .image(
                                //                                   width: 19.w,
                                //                                   height: 9.w),
                                //                         Positioned(
                                //                             bottom: 10.w,
                                //                             child: Text(
                                //                               logic.tabNameList[
                                //                                   index],
                                //                               style: TextStyles
                                //                                   .regular
                                //                                   .copyWith(
                                //                                 fontSize: logic
                                //                                             .tabbarIndex
                                //                                             .value ==
                                //                                         index
                                //                                     ? 16.sp
                                //                                     : 14.sp,
                                //                                 color: logic.tabbarIndex
                                //                                             .value ==
                                //                                         index
                                //                                     ? Colours.white
                                //                                     : Colours
                                //                                         .color5C5C6E,
                                //                               ),
                                //                             )),
                                //                       ],
                                //                     ),
                                //                   );
                                //                 });
                                //               })),
                                //         ),
                                //       ),
                                //     ),
                              ];
                            },
                            body: TabBarView(
                              controller: logic.tabController,
                              children: [
                                TabHomeItemPage1(
                                  key: const Key("1"),
                                ),
                                TabHomeItemPage2(
                                  key: const Key("2"),
                                ),
                                TabHomeItemPage3(
                                  key: const Key("3"),
                                ),
                                const TabHomeItemPage4(
                                  key: Key("4"),
                                ),
                              ],
                            ),
                          )),
                    ),
                  ],
                ),
        );
      }),
    );
  }

  Container tabBarWidget() {
    return Container(
      width: double.infinity,
      height: 35.w,
      alignment: Alignment.centerLeft,
      color: Colours.bg_color,
      margin: EdgeInsets.only(left: 16.w, bottom: 10.w),
      child: TabBar(
          controller: logic.tabController,
          unselectedLabelColor: Colours.color5C5C6E,
          unselectedLabelStyle: TextStyle(
              fontSize: 18.sp,
              color: Colours.color5C5C6E,
              fontWeight: FontWeight.w600),
          labelColor: Colours.white,
          labelStyle: TextStyle(
              fontSize: 20.sp,
              color: Colours.white,
              fontWeight: FontWeight.w600),
          isScrollable: true,
          // labelPadding: EdgeInsets.only(left: 20.w, right: 20.w),
          indicatorPadding: EdgeInsets.zero,
          dividerColor: Colors.transparent,
          dividerHeight: 0,
          labelPadding: const EdgeInsets.symmetric(horizontal: 4.0), // 调整标签间的间距
          indicatorSize: TabBarIndicatorSize.label,
          padding: EdgeInsets.zero,
          indicatorColor: Colors.transparent,
          physics: const NeverScrollableScrollPhysics(),
          tabAlignment: TabAlignment.start,
          tabs: List.generate(logic.tabNameList.length, (index) {
            return SizedBox(
              width: 50.w,
              height: 40.w,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  if (logic.tabbarIndex.value == index)
                    WxAssets.images.imgCheckIn2.image(width: 19.w, height: 9.w),
                  Positioned(
                      bottom: 10.w,
                      child: Text(
                        logic.tabNameList[index],
                        style: TextStyles.regular.copyWith(
                          fontSize:
                              logic.tabbarIndex.value == index ? 16.sp : 14.sp,
                          color: logic.tabbarIndex.value == index
                              ? Colours.white
                              : Colours.color5C5C6E,
                        ),
                      )),
                ],
              ),
            );
          })),
    );
  }

  Widget banner() {
    return Container(
      width: double.infinity,
      height: 185.w,
      margin: EdgeInsets.only(bottom: 10.w),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Stack(
        children: [
          Obx(
            () => CarouselSlider(
              carouselController: logic.carouselController,
              items: logic.imgList.map((item) {
                return Builder(
                  builder: (BuildContext context) {
                    return GestureDetector(
                      onTap: () => AppPage.to(Routes.rankingsPage),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(18.r), // 设置圆角
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                          ),
                          child: CachedNetworkImage(
                            imageUrl: item,
                            fit: BoxFit.fill,
                          ),
                        ),
                      ),
                    );
                  },
                );
              }).toList(),
              options: CarouselOptions(
                autoPlay: true,
                autoPlayInterval: const Duration(seconds: 3),
                enlargeCenterPage: false,
                // 不放大当前页面
                viewportFraction: 1,
                // 每个页面占满宽度
                aspectRatio: 345 / 185,
                onPageChanged: (index, reason) {
                  logic.currentIndex.value = index;
                },
              ),
            ),
          ),
          Obx(
            () => Visibility(
              visible: logic.imgList.length > 1,
              child: Align(
                alignment: const Alignment(0.0, 0.9),
                child: AnimatedSmoothIndicator(
                  duration: const Duration(milliseconds: 500),
                  activeIndex: logic.currentIndex.value,
                  count: logic.imgList.length,
                  effect: const ExpandingDotsEffect(
                    dotColor: Colors.black,
                    activeDotColor: Colors.white,
                    dotHeight: 8,
                    dotWidth: 8,
                    expansionFactor: 2.25,
                    spacing: 5,
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Container teamAppBar(context) {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      child: Row(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.to(Routes.highlightsPage);
            },
            child: Container(
              margin: EdgeInsets.only(left: 15.w),
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 7.w),
              decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(
                      "assets/images/home_img_bottom.png",
                    ),
                    fit: BoxFit.fill),
              ),
              child: Row(
                children: [
                  WxAssets.images.tabHomeImgPlay
                      .image(width: 14.w, height: 14.w),
                  SizedBox(
                    width: 8.w,
                  ),
                  Text(
                    S.current.highlights,
                    style: TextStyles.regular.copyWith(fontSize: 12.sp),
                  )
                  // WxAssets.images.homeImgBottom;
                ],
              ),
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.to(Routes.rankingsPage);
            },
            child: Container(
              margin: EdgeInsets.only(left: 15.w),
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 7.w),
              decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(
                      "assets/images/home_img_bottom_long.png",
                    ),
                    fit: BoxFit.fill),
              ),
              child: Row(
                children: [
                  WxAssets.images.fiveStarsIcon
                      .image(width: 14.w, height: 14.w),
                  SizedBox(
                    width: 8.w,
                  ),
                  Text(
                    S.current.tab_home_bottom,
                    style: TextStyles.regular.copyWith(fontSize: 12.sp),
                  )
                  // WxAssets.images.homeImgBottom;
                ],
              ),
            ),
          ),
          const Spacer(),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              AppPage.to(Routes.messageTypePage, needLogin: true);
            },
            child: Container(
              width: 53.w,
              padding: EdgeInsets.only(right: 20.w),
              child: Stack(
                alignment: Alignment.centerRight,
                children: [
                  WxAssets.images.homeMessage.image(width: 20.w, height: 20.w),
                  Positioned(
                    top: 1,
                    child: Obx(() => UserManager.instance.messageHasUnreadModel
                                .value?.hasUnread ==
                            true
                        ? Container(
                            width: 6.w,
                            height: 6.w,
                            decoration: BoxDecoration(
                                color: Colours.red,
                                borderRadius: BorderRadius.circular(3.r)),
                          )
                        : const SizedBox()),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 自定义委托类
class _MinimalTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _MinimalTabBarDelegate({required this.tabBar});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colours.color0F0F16,
      child: tabBar,
    );
  }

  @override
  double get maxExtent => 42.w; // 最小高度

  @override
  double get minExtent => 42.w; // 最小高度

  @override
  bool shouldRebuild(_MinimalTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
}
