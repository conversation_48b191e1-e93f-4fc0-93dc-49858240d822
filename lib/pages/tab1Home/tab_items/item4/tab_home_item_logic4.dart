import 'dart:developer';
import 'package:flutter/widgets.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/home_team_list_model.dart';

class TabHomeItemLogic4 extends GetxController {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  TextEditingController txtController1 = TextEditingController();
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <HomeTeamListModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    getdataList(isLoad: false, controller: refreshController);
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    String? teamName,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'pageIndex': dataFag["page"] ?? 1,
      'pageSize': 10,
      'teamName': teamName,
    };
    log("getTeamList33-${param}");
    var res = await Api().get(ApiUrl.getTeamList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["result"] ?? [];
      List<HomeTeamListModel> modelList =
          list.map((e) => HomeTeamListModel.fromJson(e)).toList();
      log("getTeamList33-${res.data}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 10) {
          controller.loadNoData();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
