import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/home_hot_record_model.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/widgets/match_item_widget.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item2/tab_home_item_logic2.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item4/competitions_list_item_widget.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///首页 一级页面->赛事
class TabHomeItemPage2 extends StatelessWidget {
  TabHomeItemPage2({super.key});
  final logic = Get.put(TabHomeItemLogic2());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: SizedBox(
                height: 20.w,
              ),
            ),
            _hotGamesWidget(context),
            _hotSchedule(context),
          ],
        );
      }),
    );
  }

  Widget _hotGamesWidget(context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(S.current.hot_games,
              margin: EdgeInsets.only(top: 6.w), fontSize: 16, rightOnTap: () {
            log('!!!!!!!!!');
            AppPage.to(
              Routes.scheduleHomePage,
            );
          }),
          logic.isFrist.value
              ? buildLoad(isShowGif: false)
              : (logic.homeHotRecordModel.value.matches?.isEmpty ?? true)
                  ? myNoDataView(context,
                      msg: S.current.No_data_available,
                      imagewidget: WxAssets.images.teamInfoNodata
                          .image(width: 107.w, height: 72.w),
                      height: 2.w)
                  : Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(
                            logic.homeHotRecordModel.value.matches?.length ?? 0,
                            (position) {
                          final match =
                              logic.homeHotRecordModel.value.matches![position];
                          if (match == null) {
                            return const SizedBox
                                .shrink(); // Return empty widget for null matches
                          }
                          return MatchItemWidget.fromMatchesModel(
                            model: match,
                            showStatus: true,
                          );
                        }),
                      ),
                    )
        ],
      ),
    );
  }

  Widget _hotSchedule(context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildRowTitleWidget(S.current.hot_schedule,
              margin: EdgeInsets.only(top: 6.w), fontSize: 16, rightOnTap: () {
            AppPage.to(Routes.scheduleHomePage, arguments: {
              'type': 1,
            });
          }),
          logic.isFrist.value
              ? buildLoad(isShowGif: false)
              : (logic.homeHotRecordModel.value.competitions?.isEmpty ?? true)
                  ? myNoDataView(context,
                      msg: S.current.No_data_available,
                      imagewidget: WxAssets.images.teamInfoNodata
                          .image(width: 107.w, height: 72.w),
                      height: 2.w)
                  : Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(
                            logic.homeHotRecordModel.value.competitions
                                    ?.length ??
                                0, (position) {
                          return CompetitionsListItemWidget(
                            model: logic.homeHotRecordModel.value
                                    .competitions![position] ??
                                HomeHotRecordModelCompetitions(),
                          );
                        }),
                      ),
                    )
        ],
      ),
    );
  }

  _hotSchedulelistItemWidget(HomeHotRecordModelCompetitions? model, int index) {
    String formattedStartDate = "";
    if ((model?.startTime ?? '') != "") {
      DateTime parsedStartDate = DateTime.parse(model?.startTime ?? '');
      formattedStartDate = DateFormat("yyyy.MM.dd").format(parsedStartDate);
    }
    String formattedEndDate = "";
    if ((model?.endTime ?? '') != "") {
      DateTime parsedEndDate = DateTime.parse(model?.endTime ?? '');
      formattedEndDate = DateFormat("yyyy.MM.dd").format(parsedEndDate);
    }
    return Column(
      children: [
        InkWell(
          onTap: () => AppPage.to(Routes.competitionDetailPage,
              arguments: {'competitionId': model?.competitionId}),
          child: Container(
            height: 130.w,
            margin: EdgeInsets.only(bottom: 15.w),
            padding: EdgeInsets.all(15.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.all(Radius.circular(8.r))),
            child: Row(
              children: [
                ClipRRect(
                    borderRadius: BorderRadius.circular(8.w),
                    child: CachedNetworkImage(
                      imageUrl: model?.arenaImageUrl ?? "",
                      width: 100.w,
                      height: 100.w,
                      fit: BoxFit.fill,
                    )),
                SizedBox(
                  width: 15.w,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        model?.competitionName ?? '',
                        style: TextStyles.semiBold14.copyWith(height: 1.5),
                        maxLines: 2,
                      ),
                      Text(
                        '报名截止：${model?.registrationDeadline?.split(' ').first}',
                        style: TextStyles.display12,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            height: 20,
                            padding: EdgeInsets.symmetric(horizontal: 9.w),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              gradient: model?.status == 1
                                  ? const LinearGradient(colors: [
                                      Color(0xFF7732ED),
                                      Color(0xFFA555EF),
                                    ])
                                  : null,
                              color: _getStatusColor(model?.status ?? 0),
                              borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(8.r),
                                  topRight: Radius.circular(8.r)),
                            ),
                            child: Text(
                              _getStatusStr(model?.status ?? 0),
                              style: TextStyles.display10.copyWith(
                                  color: model?.status == 4
                                      ? Colours.color5C5C6E
                                      : Colours.white,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                          const SizedBox(
                            width: 6,
                          ),
                          Text(
                            '$formattedStartDate-$formattedEndDate',
                            style: TextStyles.display12
                                .copyWith(color: Colours.colorA8A8BC),
                          )
                        ],
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }

  _getStatusStr(int status) {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '报名中';
      case 2:
        return '待开赛';
      case 3:
        return '进行中';
      case 4:
        return '已结束';
      default:
        return '未开始';
    }
  }

  _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colours.color6435E9;
      case 1:
        return Colours.color6435E9;
      case 2:
        return Colours.color6435E9;
      case 3:
        return Colours.colorFF661A;
      case 4:
        return const Color(0xFF262626);
      default:
        return const Color(0xFF262626);
    }
  }
}
