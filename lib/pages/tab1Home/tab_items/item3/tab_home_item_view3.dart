import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/home_challenge_list_model.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item3/tab_home_item_logic3.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///首页 一级页面->约战
class TabHomeItemPage3 extends StatelessWidget {
  TabHomeItemPage3({super.key});
  final logic = Get.put(TabHomeItemLogic3());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Obx(() {
        return !LocationUtils.instance.havePermission.value
            ? _location()
            : SmartRefresher(
                controller: logic.refreshController,
                footer: buildFooter(),
                header: buildClassicHeader(),
                enablePullDown: true,
                enablePullUp: logic.dataList.isNotEmpty,
                onRefresh: () {
                  logic.getdataList(isLoad: false);
                },
                onLoading: () {
                  logic.getdataList();
                },
                physics: const AlwaysScrollableScrollPhysics(),
                //  physics: const NeverScrollableScrollPhysics(),
                child: (logic.dataFag["isFrist"] as bool)
                    ? buildLoad()
                    : logic.dataList.isEmpty
                        ? SizedBox(
                            height: 300.w,
                            child: myNoDataView(
                              context,
                              msg: S.current.No_data_available,
                              imagewidget: WxAssets.images.icGameNo
                                  .image(width: 150.w, height: 150.w),
                            ))
                        : ListView.builder(
                            scrollDirection: Axis.vertical,
                            shrinkWrap: true,
                            padding: EdgeInsets.only(bottom: 40.w),
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: logic.dataList.length,
                            itemBuilder: (context, position) {
                              return _listItemWidget(logic.dataList[position]);
                            }),
              );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          AppPage.to(Routes.myBattlePage);
        },
        backgroundColor: Colors.transparent, // 提示信息（长按显示）
        child: SizedBox(
          width: 50.w,
          height: 50.w,
          child: WxAssets.images.homeYueZhan.image(width: 50.w, height: 50.w),
        ), // 按钮背景颜色
      ),
    );
  }

  Widget _location() {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 71.w,
          ),
          WxAssets.images.icHomeLocationHint
              .image(width: 104.w, fit: BoxFit.fill),
          SizedBox(
            height: 30.w,
          ),
          Text(
            S.current.location_tips,
            style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
          ),
          SizedBox(
            height: 30.w,
          ),
          WxButton(
            width: 125.w,
            height: 40.w,
            borderRadius: BorderRadius.circular(20.w),
            backgroundColor: Colours.color22222D,
            text: S.current.open_now,
            textStyle: TextStyles.regular,
            onPressed: logic.openSettings,
          ),
        ],
      ),
    );
  }

  /// 构建列表项
  Widget _listItemWidget(HomeChallengeListModel item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (item.teamId == '0') {
          AppPage.to(Routes.battleDetailPage,
              arguments: {'challengeId': item.id});
        } else {
          AppPage.to(Routes.battleDetailTeamPage,
              arguments: {'challengeId': item.id});
        }
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(8.r)),
        child: Column(
          children: [
            SizedBox(
              height: 44.w,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 4.w,
                    height: 12.w,
                    margin: EdgeInsets.only(right: 8.r),
                    decoration: BoxDecoration(
                        gradient: const LinearGradient(
                            colors: [
                              Colours.color7732ED,
                              Colours.colorA555EF,
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight),
                        borderRadius: BorderRadius.circular(10.r)),
                  ),
                  Expanded(
                    child: Text(
                      // 使用 Flexible 替代 Expanded
                      item.challengeTitle ?? "",
                      maxLines: 1,
                      style: TextStyles.regular.copyWith(
                          fontWeight: FontWeight.w600,
                          overflow: TextOverflow.ellipsis),
                    ),
                  ),
                  SizedBox(
                    width: 15.w,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Transform.translate(
                        offset: Offset(15.w, 0.w),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 15.w, vertical: 4.w),
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                  colors: [
                                    Colours.colorFFECC1,
                                    Colours.colorE7CEFF,
                                    Colours.colorD1EAFF
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight),
                              borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(8.r),
                                  bottomLeft: Radius.circular(8.r))),
                          child: Text(
                            item.challengeType == 1
                                ? S.current.Full_time
                                : S.current.Half_time,
                            style: TextStyles.regular.copyWith(
                                fontSize: 10.sp, color: Colours.color191921),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const Divider(
              color: Colours.color5C5C6E,
              height: 0,
              thickness: 0.5,
            ),
            SizedBox(
              height: 10.w,
            ),
            Row(
              children: [
                Expanded(
                    child: Column(
                  children: [
                    buildRowWidget(
                        S.current.Squadron_Battle_tip1, item.teamName ?? ""),
                    buildRowWidget(
                        S.current.Squadron_Battle_tip2, "${item.createTime}"),
                    buildRowWidget(
                        S.current.Squadron_Battle_tip3,
                        item.challengeCost == 1
                            ? S.current.Squadron_Battle_tip10
                            : S.current.Squadron_Battle_tip11),
                    buildRowWidget(
                        S.current.Squadron_Battle_tip4,
                        item.challengeStrength == 1
                            ? S.current.Squadron_Battle_tip6
                            : item.challengeStrength == 2
                                ? S.current.Squadron_Battle_tip7
                                : item.challengeStrength == 3
                                    ? S.current.Squadron_Battle_tip8
                                    : S.current.Squadron_Battle_tip9),
                  ],
                )),
                Container(
                  width: 68.w,
                  height: 28.w,
                  decoration: item.status == 0
                      ? BoxDecoration(
                          border: Border.all(
                            width: 1,
                            color: Colors.transparent,
                          ),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(32)),
                        )
                      : BoxDecoration(
                          border: Border.all(
                            width: 1,
                            color: Colours.color5C5C6E,
                          ),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(32)),
                        ),
                  child: item.status == 0
                      ? Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                Color(0xFF7732ED),
                                Color(0xFFA555EF),
                              ],
                            ),
                            borderRadius: BorderRadius.all(Radius.circular(32)),
                          ),
                          child: Container(
                            margin: const EdgeInsets.all(1),
                            decoration: const BoxDecoration(
                              color: Color(0xFF191921),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(31)),
                            ),
                            child: Center(
                              child: ShaderMask(
                                shaderCallback: (bounds) =>
                                    const LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [
                                    Color(0xFF7732ED),
                                    Color(0xFFA555EF),
                                  ],
                                ).createShader(bounds),
                                child: Text(
                                  '约战中',
                                  style: TextStyles.display12.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                      : Center(
                          child: Text(
                            item.status == 1 ? '已应战' : '已完成',
                            style: TextStyles.display12.copyWith(
                              color: Colours.color5C5C6E,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                )
              ],
            ),
            SizedBox(
              height: 10.w,
            ),
            const Divider(
              color: Colours.color5C5C6E,
              height: 0,
              thickness: 0.5,
            ),
            SizedBox(
              height: 48.w,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  MyImage(
                    item.avatar ?? "",
                    width: 18.w,
                    height: 18.w,
                    radius: 20.r,
                    placeholderImage: "my_team_head4.png",
                    errorImage: "my_team_head4.png",
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  Expanded(
                    child: Text(
                      // 使用 Flexible 替代 Expanded
                      item.createdName ?? "",
                      maxLines: 1,
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          overflow: TextOverflow.ellipsis,
                          color: Colours.color5C5C6E),
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  Text(
                    // 使用 Flexible 替代 Expanded
                    "${item.distance ?? ""}KM",
                    maxLines: 1,
                    style: TextStyles.regular.copyWith(
                        fontSize: 12.sp,
                        overflow: TextOverflow.ellipsis,
                        color: Colours.color5C5C6E),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container buildRowWidget(String name, String value) {
    return Container(
      height: 35.w,
      width: double.infinity,
      alignment: Alignment.centerLeft,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            maxLines: 1,
            style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
          ),
          SizedBox(
            width: 15.w,
          ),
          Expanded(
            child: Text(
              // 使用 Flexible 替代 Expanded
              value,
              maxLines: 1,
              style:
                  TextStyles.regular.copyWith(overflow: TextOverflow.ellipsis),
            ),
          ),
        ],
      ),
    );
  }
}
