import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/home_challenge_list_model.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';

class TabHomeItemLogic3 extends GetxController {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  StreamSubscription? subscription;
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var init = false.obs;
  //数据列表
  var dataList = <HomeChallengeListModel>[].obs;
  @override
  Future<void> onInit() async {
    super.onInit();
    if (Platform.isAndroid) {
      if (await LocationUtils.instance.checkPermission()) {
        getdataList();
      } else {
        init.value = true;
      }
    } else {
      if (await LocationUtils.instance.requestPermission(Get.context!)) {
        getdataList();
      } else {
        init.value = true;
      }
    }

    subscription = BusUtils.instance.on((action) {
      if (EventBusKey.getLocation == action.key) {
        getdataList();
      }
    });
    getdataList(isLoad: false);
  }

  //获得最新列表
  getdataList({isLoad = true}) async {
    if (await LocationUtils.instance.checkPermission()) {
      await LocationUtils.instance.getCurrentPosition();
    }

    final position = LocationUtils.instance.position;
    if (position == null) {
      init.value = true;
      return;
    }
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'page': dataFag["page"] ?? 1,
      'limit': 10,
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
    };
    log("homeChallengeList33-${param}");
    var res = await Api().get(ApiUrl.homeChallengeList, queryParameters: param);
    init.value = true;
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<HomeChallengeListModel> modelList =
          list.map((e) => HomeChallengeListModel.fromJson(e)).toList();
      log("homeChallengeList33-${res.data}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 10) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      } else {
        refreshController.resetNoData();
        dataList.assignAll(modelList);
        refreshController.refreshCompleted();
      }
    } else {
      refreshController.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
  }

  void openSettings() async {
    final result = await WxStorage.instance.getBool('requestPermission');
    if (Platform.isAndroid && result == null) {
      WxStorage.instance.setBool('requestPermission', true);
      if (await LocationUtils.instance.requestPermission(Get.context!)) {
        getdataList();
      }
    } else {
      LocationUtils.instance.openSettings();
    }
  }
}
