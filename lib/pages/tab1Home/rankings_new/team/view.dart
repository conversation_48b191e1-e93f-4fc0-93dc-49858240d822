import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/team/logic.dart';
import 'package:shoot_z/pages/tab1Home/rankings_new/team/state.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class TeamView extends StatefulWidget {
  final int index;
  const TeamView({super.key, required this.index});

  @override
  State<TeamView> createState() => _TeamViewState();
}

class _TeamViewState extends State<TeamView> {
  // 声明 logic 变量但不立即初始化
  late final TeamLogic logic;
  late final TeamState state;
  @override
  void initState() {
    super.initState();
    // 在 initState 中初始化 logic
    logic =
        Get.put(TeamLogic(index: widget.index), tag: widget.index.toString());
    state = Get.find<TeamLogic>(tag: widget.index.toString()).state;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          if (state.teamRankingModel.isNotEmpty &&
              state.teamRankingModel.length >= 3)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 91.5.w,
                  height: 160.w,
                  margin: EdgeInsets.only(top: 40.w),
                  decoration: BoxDecoration(
                      // color: Colors.red,
                      image: DecorationImage(
                          image: WxAssets.images.rankingOtherBg.provider(),
                          fit: BoxFit.fill)),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 0,
                        left: 0,
                        child: Container(
                          width: 20.w,
                          height: 20.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(colors: [
                                Colours.colorFFF9DC,
                                Colours.colorE4C8FF,
                                Colours.colorE5F3FF
                              ]),
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(8.r),
                                  bottomRight: Radius.circular(8.r))),
                          child: Text(
                            '2',
                            style: TextStyles.semiBold14.copyWith(
                                fontFamily: 'DIN', color: Colours.color161212),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 10.w,
                        left: 0,
                        right: 0,
                        child: Column(
                          children: [
                            InkWell(
                                onTap: () =>
                                    AppPage.to(Routes.teamInfoPage, arguments: {
                                      'teamId':
                                          state.teamRankingModel[1].teamId ??
                                              '0'
                                    }),
                                child: Container(
                                  width: 55.w,
                                  height: 61.w,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: WxAssets.images.numberTwoHeadCircle
                                          .provider(), // 本地边框图片
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                  child: Center(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20.w),
                                      child: CachedNetworkImage(
                                        imageUrl:
                                            state.teamRankingModel[1].logo ??
                                                '',
                                        width: 40.w,
                                        height: 40.w,
                                        fit: BoxFit.fill,
                                      ),
                                    ),
                                  ),
                                )),
                            SizedBox(
                              height: 8.w,
                            ),
                            Text(state.teamRankingModel[1].name ?? '',
                                style: TextStyles.regular
                                    .copyWith(fontSize: 14.sp),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis),
                            SizedBox(
                              height: 8.w,
                            ),
                            Text(
                              state.teamRankingModel[1].rankData ?? '',
                              style: TextStyles.bold.copyWith(fontSize: 16.sp),
                            ),
                            SizedBox(
                              height: 8.w,
                            ),
                            Image.asset(
                                width: 24.w,
                                height: 24.w,
                                state.teamRankingModel[1].imagePath ??
                                    'assets/images/one_stars_icon.png'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 91.5.w,
                  height: 160.w,
                  margin: EdgeInsets.only(top: 0.w),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.numberOneBg.provider(),
                          fit: BoxFit.fill)),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 0,
                        left: 0,
                        child: Container(
                          width: 20.w,
                          height: 20.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ]),
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(8.r),
                                  bottomRight: Radius.circular(8.r))),
                          child: Text(
                            '1',
                            style: TextStyles.semiBold14
                                .copyWith(fontFamily: 'DIN'),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 10.w,
                        left: 0,
                        right: 0,
                        child: Column(
                          children: [
                            InkWell(
                                onTap: () =>
                                    AppPage.to(Routes.teamInfoPage, arguments: {
                                      'teamId':
                                          state.teamRankingModel[0].teamId ??
                                              '0'
                                    }),
                                child: Container(
                                  width: 55.w,
                                  height: 61.w,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: WxAssets.images.numberOneHeadCircle
                                          .provider(), // 本地边框图片
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  child: Center(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20.w),
                                      child: CachedNetworkImage(
                                        imageUrl:
                                            state.teamRankingModel[0].logo ??
                                                '',
                                        width: 40.w,
                                        height: 40.w,
                                        fit: BoxFit.fill,
                                      ),
                                    ),
                                  ),
                                )),
                            SizedBox(
                              height: 8.w,
                            ),
                            Text(state.teamRankingModel[0].name ?? '',
                                style: TextStyles.regular
                                    .copyWith(fontSize: 14.sp),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis),
                            SizedBox(
                              height: 8.w,
                            ),
                            Text(
                              state.teamRankingModel[0].rankData ?? '',
                              style: TextStyles.bold.copyWith(
                                  fontSize: 16.sp,
                                  color: const Color(0xFF6234E3)),
                            ),
                            SizedBox(
                              height: 8.w,
                            ),
                            Image.asset(
                                width: 24.w,
                                height: 24.w,
                                state.teamRankingModel[0].imagePath ??
                                    'assets/images/one_stars_icon.png'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 91.5.w,
                  height: 160.w,
                  margin: EdgeInsets.only(top: 40.w),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.rankingOtherBg.provider(),
                          fit: BoxFit.fill)),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 0,
                        left: 0,
                        child: Container(
                          width: 20.w,
                          height: 20.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(colors: [
                                Colours.color884C0B,
                                Colours.colorD15B1B
                              ]),
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(8.r),
                                  bottomRight: Radius.circular(8.r))),
                          child: Text(
                            '3',
                            style: TextStyles.semiBold14
                                .copyWith(fontFamily: 'DIN'),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 10.w,
                        left: 0,
                        right: 0,
                        child: Column(
                          children: [
                            InkWell(
                                onTap: () =>
                                    AppPage.to(Routes.teamInfoPage, arguments: {
                                      'teamId':
                                          state.teamRankingModel[2].teamId ??
                                              '0'
                                    }),
                                child: Container(
                                  width: 55.w,
                                  height: 61.w,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: WxAssets
                                          .images.numberThreeHeadCircle
                                          .provider(), // 本地边框图片
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  child: Center(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20.w),
                                      child: CachedNetworkImage(
                                        imageUrl:
                                            state.teamRankingModel[2].logo ??
                                                '',
                                        width: 40.w,
                                        height: 40.w,
                                        fit: BoxFit.fill,
                                      ),
                                    ),
                                  ),
                                )),
                            SizedBox(
                              height: 8.w,
                            ),
                            Text(state.teamRankingModel[2].name ?? '',
                                style: TextStyles.regular
                                    .copyWith(fontSize: 14.sp),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis),
                            SizedBox(
                              height: 8.w,
                            ),
                            Text(
                              state.teamRankingModel[2].rankData ?? '',
                              style: TextStyles.bold.copyWith(fontSize: 16.sp),
                            ),
                            SizedBox(
                              height: 8.w,
                            ),
                            Image.asset(
                                width: 24.w,
                                height: 24.w,
                                state.teamRankingModel[2].imagePath ??
                                    'assets/images/one_stars_icon.png'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          SizedBox(
            height: 20.w,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                  alignment: Alignment.center,
                  width: 50.w,
                  child: Text('排名',
                      style: TextStyles.regular.copyWith(fontSize: 12.sp))),
              Container(
                  alignment: Alignment.center,
                  width: 90.w,
                  child: Text('球队',
                      style: TextStyles.regular.copyWith(fontSize: 12.sp))),
              GestureDetector(
                onTap: () => AppPage.to(Routes.rankingRulesPage),
                child: SizedBox(
                  width: 50.w,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text('段位',
                          style: TextStyles.regular.copyWith(fontSize: 12.sp)),
                      SizedBox(
                        width: 5.w,
                      ),
                      WxAssets.images.queryIcon
                          .image(width: 12.w, height: 12.w),
                    ],
                  ),
                ),
              ),
              GestureDetector(
                  onTap: () => AppPage.to(Routes.rankingRulesPage),
                  child: Row(
                    children: [
                      Text('分数',
                          style: TextStyles.regular.copyWith(fontSize: 12.sp)),
                      SizedBox(
                        width: 5.w,
                      ),
                      WxAssets.images.queryIcon
                          .image(width: 12.w, height: 12.w),
                    ],
                  ))
            ],
          ),
          SizedBox(height: 10.w),
          Expanded(child: Obx(() {
            return state.requestError.value
                ? const SizedBox.shrink()
                : state.teamRankingModel.isEmpty
                    ? buildLoad()
                    : ListView.builder(
                        padding: EdgeInsets.only(
                            bottom: (MediaQuery.of(context).padding.bottom > 0
                                    ? MediaQuery.of(context).padding.bottom
                                    : 20.w) +
                                48.w),
                        itemCount: state.teamRankingModel.length > 3
                            ? (state.teamRankingModel.length - 3)
                            : 0,
                        itemBuilder: (context, index) {
                          final actualIndex = index + 3;
                          return Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    SizedBox(
                                        width: 25.w, // 固定宽度
                                        child: Text(
                                          '${actualIndex + 1}',
                                          style: TextStyles.semiBold14
                                              .copyWith(fontFamily: 'DIN'),
                                        )),
                                    SizedBox(
                                      width: 35.w, // 固定宽度
                                      child: Text(
                                        state.teamRankingModel[actualIndex]
                                                    .changedIndex ==
                                                0
                                            ? ' —'
                                            : (state
                                                            .teamRankingModel[
                                                                actualIndex]
                                                            .changedIndex ??
                                                        0) >
                                                    0
                                                ? '+${state.teamRankingModel[actualIndex].changedIndex}'
                                                : '${state.teamRankingModel[actualIndex].changedIndex}',
                                        textAlign: TextAlign.left, // 内容居左
                                        style: TextStyles.regular.copyWith(
                                            color: (state
                                                            .teamRankingModel[
                                                                actualIndex]
                                                            .changedIndex ??
                                                        0) >
                                                    0
                                                ? Colours.colorFF3F3F
                                                : (state
                                                                .teamRankingModel[
                                                                    actualIndex]
                                                                .changedIndex ??
                                                            0) <
                                                        0
                                                    ? Colours.color0DB600
                                                    : Colours.colorA8A8BC,
                                            fontSize: 14.sp,
                                            fontFamily: 'DIN'),
                                      ),
                                    ),
                                  ],
                                ),
                                InkWell(
                                    onTap: () => AppPage.to(Routes.teamInfoPage,
                                            arguments: {
                                              'teamId': state
                                                      .teamRankingModel[
                                                          actualIndex]
                                                      .teamId ??
                                                  '0'
                                            }),
                                    child: Row(
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12.w),
                                          child: CachedNetworkImage(
                                            imageUrl: state
                                                    .teamRankingModel[
                                                        actualIndex]
                                                    .logo ??
                                                '',
                                            width: 24.w,
                                            height: 24.w,
                                            fit: BoxFit.fill,
                                          ),
                                        ).marginSymmetric(vertical: 6.w),
                                        SizedBox(
                                          width: 8.w,
                                        ),
                                        SizedBox(
                                          width: 70.w,
                                          child: Text(
                                            state.teamRankingModel[actualIndex]
                                                    .name ??
                                                '',
                                            style: TextStyles.regular
                                                .copyWith(fontSize: 12.sp),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    )),
                                SizedBox(
                                  width: 40.w,
                                  child: Image.asset(
                                      width: 24.w,
                                      height: 21.5.w,
                                      state.teamRankingModel[actualIndex]
                                              .imagePath ??
                                          'assets/images/one_stars_icon.png'),
                                ),
                                Container(
                                  alignment: Alignment.centerRight,
                                  width: 40.w,
                                  child: Text(
                                      state.teamRankingModel[actualIndex]
                                              .rankData ??
                                          '',
                                      style: TextStyles.bold
                                          .copyWith(fontSize: 14.sp),
                                      textAlign: TextAlign.center),
                                ),
                              ]);
                        });
          })),
        ],
      ).marginOnly(left: 20.w, right: 20.w);
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }
}
