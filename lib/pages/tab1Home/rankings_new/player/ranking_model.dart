import 'package:shoot_z/generated/l10n.dart';

///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
const String _jsonKeyPlayerRankingModelMonth = 'month';
const String _jsonKeyPlayerRankingModelBillboard = 'billboard';
const String _jsonKeyPlayerRankingModelMyIndex = 'myIndex';
const String _jsonKeyPlayerRankingModelMyLogo = 'myLogo';
const String _jsonKeyPlayerRankingModelMyName = 'myName';
const String _jsonKeyPlayerRankingModelMyRankData = 'myRankData';
const String _jsonKeyPlayerRankingModelMyChangedNo = 'myChangedNo';
const String _jsonKeyPlayerRankingModelMyUserId = 'myUserId';
const String _jsonKeyPlayerRankingModelBillboardIndex = 'index';
const String _jsonKeyPlayerRankingModelBillboardLogo = 'logo';
const String _jsonKeyPlayerRankingModelBillboardName = 'name';
const String _jsonKeyPlayerRankingModelBillboardRankData = 'rankData';
const String _jsonKeyPlayerRankingModelBillboardChangedNo = 'changedNo';
const String _jsonKeyPlayerRankingModelBillboardFake = 'fake';
const String _jsonKeyPlayerRankingModelBillboardUserId = 'userId';

class RankingModelBillboard {
/*
{
  "index": 1,
  "logo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app_test/avatar/1730129907_tmp_b351b48c620b618a213530cd5b591971.jpg",
  "name": "败球的小孩子",
  "rankData": "1108",
  "changedNo": 0,
  "fake": false
} 
*/

  int? index;
  String? logo;
  String? name;
  String? rankData;
  String? changedNo;
  bool? fake;
  String? userId;

  RankingModelBillboard(
      {this.index,
      this.logo,
      this.name,
      this.rankData,
      this.changedNo,
      this.fake,
      this.userId});
  RankingModelBillboard.fromJson(Map<String, dynamic> json) {
    index = json[_jsonKeyPlayerRankingModelBillboardIndex]?.toInt();
    logo = json[_jsonKeyPlayerRankingModelBillboardLogo]?.toString();
    name = json[_jsonKeyPlayerRankingModelBillboardName]?.toString();
    rankData = json[_jsonKeyPlayerRankingModelBillboardRankData]?.toString();
    changedNo = json[_jsonKeyPlayerRankingModelBillboardChangedNo]?.toString();
    fake = json[_jsonKeyPlayerRankingModelBillboardFake];
    userId = json[_jsonKeyPlayerRankingModelBillboardUserId]?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data[_jsonKeyPlayerRankingModelBillboardIndex] = index;
    data[_jsonKeyPlayerRankingModelBillboardLogo] = logo;
    data[_jsonKeyPlayerRankingModelBillboardName] = name;
    data[_jsonKeyPlayerRankingModelBillboardRankData] = rankData;
    data[_jsonKeyPlayerRankingModelBillboardChangedNo] = changedNo;
    data[_jsonKeyPlayerRankingModelBillboardFake] = fake;
    data[_jsonKeyPlayerRankingModelBillboardUserId] = userId;
    return data;
  }
}

class RankingModel {
/*
{
  "month": 3,
  "billboard": [
    {
      "index": 1,
      "logo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app_test/avatar/1730129907_tmp_b351b48c620b618a213530cd5b591971.jpg",
      "name": "败球的小孩子",
      "rankData": "1108",
      "changedNo": 0,
      "fake": false
    }
  ],
  "myIndex": 0,
  "myLogo": "",
  "myName": "",
  "myRankData": "",
  "myChangedNo": 0,
  "myUserId": "0"
} 
*/

  int? month;
  List<RankingModelBillboard?>? billboard;
  int? myIndex;
  String? myLogo;
  String? myName;
  String? myRankData;
  int? myChangedNo;
  String? myUserId;

  RankingModel({
    this.month,
    this.billboard,
    this.myIndex,
    this.myLogo,
    this.myName,
    this.myRankData,
    this.myChangedNo,
    this.myUserId,
  });
  RankingModel.fromJson(Map<String, dynamic> json) {
    month = json[_jsonKeyPlayerRankingModelMonth]?.toInt();
    if (json[_jsonKeyPlayerRankingModelBillboard] != null) {
      final v = json[_jsonKeyPlayerRankingModelBillboard];
      final arr0 = <RankingModelBillboard>[];
      v.forEach((v) {
        arr0.add(RankingModelBillboard.fromJson(v));
      });
      billboard = arr0;
    }
    myIndex = json[_jsonKeyPlayerRankingModelMyIndex]?.toInt();
    myLogo = json[_jsonKeyPlayerRankingModelMyLogo]?.toString();
    myName = json[_jsonKeyPlayerRankingModelMyName]?.toString();
    myRankData = json[_jsonKeyPlayerRankingModelMyRankData]?.toString();
    myChangedNo = json[_jsonKeyPlayerRankingModelMyChangedNo]?.toInt();
    myUserId = json[_jsonKeyPlayerRankingModelMyUserId]?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data[_jsonKeyPlayerRankingModelMonth] = month;
    if (billboard != null) {
      final v = billboard;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data[_jsonKeyPlayerRankingModelBillboard] = arr0;
    }
    data[_jsonKeyPlayerRankingModelMyIndex] = myIndex;
    data[_jsonKeyPlayerRankingModelMyLogo] = myLogo;
    data[_jsonKeyPlayerRankingModelMyName] = myName;
    data[_jsonKeyPlayerRankingModelMyRankData] = myRankData;
    data[_jsonKeyPlayerRankingModelMyChangedNo] = myChangedNo;
    data[_jsonKeyPlayerRankingModelMyUserId] = myUserId;
    return data;
  }
}

class TeamRankingModel {
/*
{
  "index": 1,
  "logo": "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/app_test/avatar/1730129907_tmp_b351b48c620b618a213530cd5b591971.jpg",
  "name": "败球的小孩子",
  "rankData": "1108",
  "changedNo": 0,
  "fake": false
} 
*/

  int? index;
  String? logo;
  String? name;
  String? rankData;
  String? imagePath;
  int? changedIndex;
  String? teamId;
  TeamRankingModel(
      {this.index,
      this.logo,
      this.name,
      this.rankData,
      this.imagePath,
      this.changedIndex,
      this.teamId});
  TeamRankingModel.fromJson(Map<String, dynamic> json) {
    index = json['index']?.toInt();
    changedIndex = json['changedIndex']?.toInt();
    logo = json['logo']?.toString();
    teamId = json['teamId']?.toString();
    name = json['teamName']?.toString();
    rankData = json['rankScore']?.toString();
    final rankScore = int.tryParse(rankData ?? '') ?? 0;
    imagePath = rankScore >= 3000
        ? 'assets/images/five_stars_icon.png'
        : rankScore >= 2000
            ? 'assets/images/four_stars_icon.png'
            : rankScore >= 1500
                ? 'assets/images/three_stars_icon.png'
                : rankScore >= 1200
                    ? 'assets/images/two_stars_icon.png'
                    : 'assets/images/one_stars_icon.png';
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['changedIndex'] = changedIndex;
    data['teamId'] = teamId;
    data['index'] = index;
    data['logo'] = logo;
    data['teamName'] = name;
    data['rankScore'] = rankData;
    data['imagePath'] = imagePath;
    return data;
  }
}
