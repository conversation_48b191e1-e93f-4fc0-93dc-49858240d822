import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/network/model/invite_mini_path_model.dart';
import 'package:shoot_z/network/model/team_home_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/battle_detail_logic.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/contact_publisher_dialog.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/widgets/match_item_widget.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/pay/fluwx_utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class BattleDetailTeamPage extends StatelessWidget {
  BattleDetailTeamPage({super.key});

  final logic = Get.put(BattleDetailLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: _createDetailWidget(context),
        bottomNavigationBar: Obx(() {
          return UserManager.instance.user?.userId ==
                  logic.challengeModel.value.userId
              ? const SizedBox()
              : Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(
                      left: 15.w,
                      right: 15.w,
                      bottom: ScreenUtil().bottomBarHeight),
                  child: Row(
                    children: [
                      InkWell(
                        onTap: () async {
                          //分享到微信
                          InviteMiniPathModel inviteMiniPathModel =
                              InviteMiniPathModel(
                            appRawId: FluwxUtils.miniProgramUserName,
                            appId: FluwxUtils.appId,
                            path:
                                "pagesMatch/battle/battleDetail.html?battleId=${logic.challengeModel.value.id}&teamId=${logic.teamId}&battleType=2",
                            image: "",
                            icon: "",
                            title: "快来和我约战吧",
                          );
                          ByteData data = await rootBundle.load(
                              "assets/images/dialog_invitation.png"); //dialog_invitation
                          Uint8List imageBytes = data.buffer.asUint8List();

                          MyShareH5.shareMiniProgram(
                              inviteMiniPathModel, imageBytes);
                        },
                        child: WxAssets.images.shareSendIcon.image(),
                      ),
                      SizedBox(
                        width: 15.w,
                      ),
                      Expanded(
                          child: InkWell(
                        onTap: () {
                          if (logic.challengeModel.value.status == 2) {
                            WxLoading.showToast('约战已结束，无法联系发布者');
                            return;
                          }
                          Get.bottomSheet(
                            ContactPublisherDialog(
                              publisherPhone: logic.challengeModel.value.phone,
                            ),
                            backgroundColor: Colors.transparent,
                            isScrollControlled: true,
                          );
                        },
                        child: Container(
                          height: 44.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF
                                ],
                                begin: Alignment.bottomLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(25.r)),
                          child: Text(
                            '联系发布者',
                            style: TextStyles.semiBold14,
                          ),
                        ),
                      ))
                    ],
                  ),
                );
        }));
  }

  /// 详情数据
  _createDetailWidget(BuildContext context) {
    return Obx(() {
      if (logic.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(
            color: Colours.color7732ED,
          ),
        );
      }

      // 检查数据是否有效
      if (logic.challengeModel.value.id == null ||
          logic.challengeModel.value.id!.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64.w,
                color: Colors.grey[400],
              ),
              SizedBox(height: 16.w),
              Text(
                '暂无约战数据',
                style: TextStyles.regular.copyWith(
                  color: Colors.grey[400],
                  fontSize: 16.sp,
                ),
              ),
            ],
          ),
        );
      }
      int winCount = logic.matchList.where((item) => item.win ?? false).length;
      return Column(
        children: [
          // 顶部固定的渐变背景区域
          Container(
            width: ScreenUtil().screenWidth,
            height: 235.w,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: _buildTopContent(context),
          ),
          // 可滚动的内容区域
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _centerInfo(),
                  Container(
                    margin:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Container(
                          alignment: Alignment.centerLeft,
                          child: const TextWithIcon(title: '球队数据'),
                        ),
                        SizedBox(
                          height: 15.w,
                        ),
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                              color: Colours.color191921,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.r))),
                          padding: EdgeInsets.only(
                              top: 20.w, bottom: 20.w, left: 15.w, right: 15.w),
                          child: Wrap(
                            spacing:
                                (ScreenUtil().screenWidth - 60.w - 248.w) / 3.0,
                            runSpacing: 20.w,
                            children: [
                              _teamData(
                                  logic.challengeModel.value.teamInfo
                                          ?.avgShootCount
                                          ?.toString() ??
                                      '0',
                                  '场均出手'),
                              _teamData(
                                  logic.challengeModel.value.teamInfo?.avgScore
                                          ?.toString() ??
                                      '0',
                                  '场均得分'),
                              _teamData(
                                  logic.challengeModel.value.teamInfo
                                          ?.shootRate ??
                                      '0',
                                  '命中率'),
                              _teamData(
                                  logic.challengeModel.value.teamInfo
                                          ?.avgFreeThrowShootCount
                                          ?.toString() ??
                                      '0',
                                  '罚球数'),
                              _teamData(
                                  logic.challengeModel.value.teamInfo
                                          ?.avgFreeThrowShootRate ??
                                      '0',
                                  '罚球命中率'),
                              _teamData(
                                  logic.challengeModel.value.teamInfo
                                          ?.avgThreePointShootCount
                                          ?.toString() ??
                                      '0',
                                  '三分出手'),
                              _teamData(
                                  logic.challengeModel.value.teamInfo
                                          ?.avgThreePointShootHit
                                          ?.toString() ??
                                      '0',
                                  '三分命中'),
                              _teamData(
                                  logic.challengeModel.value.teamInfo
                                          ?.avgThreePointShootRate ??
                                      '0',
                                  '三分命中率'),
                            ],
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const TextWithIcon(title: '球队比赛'),
                            logic.matchList.isNotEmpty
                                ? Text(
                                    '$winCount胜${5 - winCount}负 最近5场',
                                    style: TextStyles.display12.copyWith(
                                        color: const Color(0xFFA8A8BC)),
                                  )
                                : const SizedBox()
                          ],
                        ).marginOnly(top: 20.w, bottom: 15.w),
                        logic.matchList.isEmpty
                            ? myNoDataView(
                                context,
                                msg: '暂无比赛',
                                imagewidget:
                                    WxAssets.images.purposeEmpty.image(),
                              )
                            : Column(
                                children: logic.matchList
                                    .map(
                                        (e) => MatchItemWidget.fromMatchesModel(
                                              model: e,
                                              showStatus: false,
                                            ))
                                    .toList(),
                              ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const TextWithIcon(title: '球队集锦'),
                            (logic.teamHomeModel.value.videos ?? []).isNotEmpty
                                ? GestureDetector(
                                    onTap: () {
                                      AppPage.to(Routes.teamInfoPage,
                                          arguments: {
                                            'teamId': logic.challengeModel.value
                                                .leftTeamId,
                                            'index': 4
                                          }).then((v) {
                                        // logic.getdataList(controller: logic.refreshController, isLoad: false);
                                      });
                                    },
                                    child: Row(
                                      children: [
                                        Text(
                                          '更多',
                                          style: TextStyles.display12.copyWith(
                                              color: Colours.colorA8A8BC),
                                        ),
                                        SizedBox(
                                          width: 5.w,
                                        ),
                                        Icon(
                                          Icons.arrow_forward_ios,
                                          size: 14.sp,
                                          color: Colours.colorA8A8BC,
                                        )
                                      ],
                                    ),
                                  )
                                : const SizedBox()
                          ],
                        ).marginOnly(top: 20.w, bottom: 15.w),
                        (logic.teamHomeModel.value.videos ?? []).isEmpty
                            ? myNoDataView(
                                context,
                                msg: '暂无集锦',
                                imagewidget: WxAssets.images.albumEmpty.image(),
                              )
                            : _listIWidget(),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      );
    });
  }

  /// 构建列表项
  Widget _listIWidget() {
    return GridView.builder(
      shrinkWrap: true, //GridView 的大小会根据其内容动态调整，只占用内容所需的空间。
      physics: const AlwaysScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // 每行两个 item
        crossAxisSpacing: 9.w,
        mainAxisSpacing: 10.w,
        childAspectRatio: ((ScreenUtil().screenWidth - 39.w) / 2) /
            (9 * ((ScreenUtil().screenWidth - 39.w) / 2) / 16 +
                30.w), // 控制每个 item 的宽高比例
      ),
      padding: EdgeInsets.only(
        bottom: 40.w,
      ),
      itemCount: (logic.teamHomeModel.value.videos ?? []).length,
      itemBuilder: (context, index) {
        TeamHomeModelVideos videosModel =
            logic.teamHomeModel.value.videos![index] ?? TeamHomeModelVideos();

        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () async {
            AppPage.to(Routes.videos, arguments: {
              "videoId": videosModel.videoId,
              "matchId": videosModel.matchId,
              "type": "0", //0合并id  1片段id
            });
          },
          child: Column(
            children: [
              SizedBox(
                width: (ScreenUtil().screenWidth - 39.w) / 2,
                height: 9 * ((ScreenUtil().screenWidth - 39.w) / 2) / 16,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    MyImage(
                      videosModel.videoCover ?? "",
                      width: ScreenUtil().screenWidth - 39.w,
                      height: 9 * (ScreenUtil().screenWidth - 39.w) / 16,
                      radius: 8.r,
                      errorImage: "error_image_width.png",
                      placeholderImage: "error_image_width.png",
                    ),
                    Positioned(
                        left: 0,
                        right: 0,
                        bottom: 0,
                        top: 0,
                        child: WxAssets.images.videoPlay
                            .image(width: 28.w, height: 28.w)),
                  ],
                ),
              ),
              SizedBox(
                height: 10.w,
              ),
              SizedBox(
                height: 20.w,
                child: Text(
                  "${videosModel.leftTeamName} vs ${videosModel.rightTeamName}",
                  style: TextStyles.regular.copyWith(
                    fontSize: 12.sp,
                    color: Colors.white,
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _teamData(String dataStr, String bottomStr) {
    return SizedBox(
      width: 62.w,
      child: Column(
        children: [
          Text(
            dataStr,
            maxLines: 1,
            style: TextStyle(
                fontFamily: 'DIN',
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.white),
          ),
          SizedBox(
            height: 10.w,
          ),
          Text(
            bottomStr,
            style: TextStyles.display12,
            maxLines: 1,
          )
        ],
      ),
    );
  }

  Widget _buildTopContent(BuildContext context) {
    DateTime parsedDate = DateTime.now(); // 默认值
    String formattedDate = '';
    String formattedTime = '';

    try {
      final matchTime = logic.challengeModel.value.matchTime;
      if (matchTime != null && matchTime.isNotEmpty) {
        parsedDate = DateTime.parse(matchTime);
        formattedDate = DateFormat("MM月dd日 EEE").format(parsedDate);
        formattedTime = DateFormat("HH:mm").format(parsedDate);
      } else {
        formattedDate = "待定";
        formattedTime = "--:--";
      }
    } catch (e) {
      // 如果日期解析失败，使用默认值
      formattedDate = "日期格式错误";
      formattedTime = "--:--";
    }

    return Column(
      children: [
        _topBarWidget(context),
        Container(
          padding:
              EdgeInsets.only(top: 10.w, bottom: 15.w, left: 15.w, right: 15.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () => AppPage.to(Routes.careerHighlightsHomePage,
                    arguments: {'userId': logic.challengeModel.value.userId}),
                child: Row(
                  children: [
                    MyImage(
                      logic.challengeModel.value.avatar ?? "",
                      width: 32.w,
                      height: 32.w,
                      radius: 16.r,
                      isAssetImage: false,
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                    Text(
                      logic.challengeModel.value.userName ?? "",
                      style: TextStyles.titleSemiBold16,
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 14.sp,
                      color: Colors.white,
                    )
                  ],
                ),
              ),
              if (UserManager.instance.user?.userId !=
                  logic.challengeModel.value.userId)
                InkWell(
                  onTap: () {
                    if (logic.challengeModel.value.status == 2) {
                      WxLoading.showToast('约战已结束，无法联系发布者');
                      return;
                    }
                    Get.bottomSheet(
                      ContactPublisherDialog(
                        publisherPhone: logic.challengeModel.value.phone,
                      ),
                      backgroundColor: Colors.transparent,
                      isScrollControlled: true,
                    );
                  },
                  child: Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.white, width: 1.w),
                        borderRadius: BorderRadius.circular(34.r)),
                    width: 70.w,
                    height: 28.w,
                    child: Text(
                      '联系TA',
                      style: TextStyles.regular
                          .copyWith(color: Colors.white, fontSize: 12.sp),
                    ),
                  ),
                ),
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            InkWell(
              onTap: () => AppPage.to(Routes.teamInfoPage, arguments: {
                'teamId': logic.challengeModel.value.leftTeamId,
              }),
              child: Column(
                children: [
                  MyImage(
                    logic.challengeModel.value.leftTeamLogo ?? "",
                    width: 46.w,
                    height: 46.w,
                    radius: 23.r,
                    isAssetImage: false,
                  ),
                  SizedBox(
                    height: 8.w,
                  ),
                  Row(
                    children: [
                      Image.asset(
                        logic.challengeModel.value.leftImagePath ?? "",
                        width: 24.w,
                        height: 24.w,
                      ),
                      SizedBox(
                        width: 2.w,
                      ),
                      Text(
                        logic.challengeModel.value.leftTeamName ?? "",
                        style: TextStyles.display14,
                      ),
                    ],
                  )
                ],
              ),
            ),
            Column(
              children: [
                Container(
                  alignment: Alignment.center,
                  height: 32.w,
                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                  decoration: BoxDecoration(
                      color: const Color(0x14FFFFFF),
                      borderRadius: BorderRadius.circular(8.r)),
                  child: Row(
                    children: [
                      WxAssets.images.icTimeWhite
                          .image(width: 14.w, height: 14.w),
                      SizedBox(
                        width: 6.w,
                      ),
                      Text(
                        formattedDate,
                        style:
                            TextStyles.display12.copyWith(color: Colors.white),
                      )
                    ],
                  ),
                ),
                SizedBox(
                  height: 7.w,
                ),
                Text(
                  formattedTime,
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 30.sp,
                      fontFamily: 'DIN',
                      fontWeight: FontWeight.bold),
                )
              ],
            ),
            Column(
              children: [
                MyImage(
                  logic.challengeModel.value.rightTeamLogo ?? "",
                  width: 46.w,
                  height: 46.w,
                  radius: 23.r,
                  isAssetImage: false,
                ),
                SizedBox(
                  height: 8.w,
                ),
                Row(
                  children: [
                    if (logic.challengeModel.value.rightTeamId != '0')
                      Image.asset(
                        logic.challengeModel.value.rightImagePath ?? "",
                        width: 24.w,
                        height: 24.w,
                      ),
                    SizedBox(
                      width: 2.w,
                    ),
                    Text(
                      logic.challengeModel.value.rightTeamName ?? "",
                      style: TextStyles.display14,
                    ),
                  ],
                )
              ],
            )
          ],
        ).marginSymmetric(horizontal: 20.w)
      ],
    );
  }

  Widget _centerInfo() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      width: double.infinity,
      decoration: BoxDecoration(
          color: Colours.color191921,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(8.r),
              bottomRight: Radius.circular(8.r))),
      padding:
          EdgeInsets.only(top: 20.w, bottom: 20.w, left: 15.w, right: 15.w),
      child: Column(
        children: [
          infoWidget('场馆', logic.challengeModel.value.arenaName ?? "暂无"),
          SizedBox(
            height: 20.w,
          ),
          infoWidget(
              '赛制', logic.challengeModel.value.challengeFormatStr ?? "暂无"),
          SizedBox(
            height: 20.w,
          ),
          infoWidget(
              '强度', logic.challengeModel.value.challengeStrengthStr ?? "暂无"),
          SizedBox(
            height: 20.w,
          ),
          infoWidget('费用', logic.challengeModel.value.challengeCostStr ?? "暂无"),
          SizedBox(
            height: 20.w,
          ),
          infoWidget('补充', logic.challengeModel.value.remark ?? "暂无"),
          if (UserManager.instance.user?.userId !=
              logic.challengeModel.value.userId)
            Container(
              margin: EdgeInsets.only(top: 20.w),
              alignment: Alignment.centerLeft,
              child: RichText(
                text: TextSpan(
                  style: TextStyles.regular.copyWith(
                    color: Colours.colorA8A8BC,
                    fontSize: 12.sp,
                  ),
                  children: [
                    const TextSpan(text: '请点击'),
                    TextSpan(
                      text: '【联系发布者】',
                      style: TextStyles.regular.copyWith(
                        color: const Color(0xFFFCE051),
                        fontSize: 12.sp,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          if (logic.challengeModel.value.status == 2) {
                            WxLoading.showToast('约战已结束，无法联系发布者');
                            return;
                          }
                          // Show contact publisher bottom sheet
                          Get.bottomSheet(
                            ContactPublisherDialog(
                              publisherPhone: logic.challengeModel.value.phone,
                            ),
                            backgroundColor: Colors.transparent,
                            isScrollControlled: true,
                          );
                        },
                    ),
                    const TextSpan(text: '与TA取得联系'),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
    //     const SizedBox(
    //       height: 40,
    //     ),
    //     Center(
    //       child: myNoDataView(
    //         context,
    //         msg: '该用户暂未选择球队',
    //         imagewidget: WxAssets.images.teamEmptyIcon
    //             .image(width: 180.w, height: 120.w),
    //       ),
    //     ),
    //   ],
    // )).marginSymmetric(horizontal: 15.w);
  }

  Widget _topBarWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 50.w,
      alignment: Alignment.center,
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 8.w, right: 10.w, top: 6.w),
            child: IconButton(
                onPressed: () {
                  AppPage.back();
                },
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                )),
          ),
          Container(
            padding: EdgeInsets.only(top: 6.w),
            child: Text(
              '约战详情',
              style: TextStyles.titleSemiBold16,
            ),
          ),
          Container(
            width: 60.w,
            padding: EdgeInsets.only(left: 10.w, right: 8.w),
          ),
        ],
      ),
    );
  }

  Widget infoWidget(String title, String info) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyles.regular
              .copyWith(color: Colours.color5C5C6E, fontSize: 14.sp),
        ),
        SizedBox(
          width: 48.w,
        ),
        Expanded(
          child: Text(
            info,
            style: TextStyles.regular
                .copyWith(color: Colours.white, fontSize: 14.sp),
          ),
        )
      ],
    );
  }
}
