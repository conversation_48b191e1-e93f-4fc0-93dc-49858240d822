import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/invite_mini_path_model.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/my_battle_logic.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/my_matches_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/pay/fluwx_utils.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的约战列表
class MyBattlePage extends StatelessWidget {
  MyBattlePage({super.key});

  final logic = Get.put(MyBattleLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的约战'),
      ),
      body: Obx(() {
        return NotificationListener(
            onNotification: (ScrollNotification note) {
              if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                logic.loadMore();
              }
              return true;
            },
            child: RefreshIndicator(
              onRefresh: logic.onRefresh,
              child: Container(
                color: Colours.bg_color,
                child: logic.init.value
                    ? (logic.myBattleModelList.isEmpty
                        ? _emptyView(context)
                        : _listView(context))
                    : buildLoad(),
              ),
            ));
      }),
      bottomNavigationBar: InkWell(
        onTap: () => _showBattleTypeBottomSheet(context),
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            '创建约战',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: constraints.maxHeight,
              child: myNoDataView(
                context,
                msg: '暂无约战，快去创建吧~',
                imagewidget: WxAssets.images.battleEmptyIcon.image(),
              ),
            ));
      },
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 15.w),
        itemCount: logic.myBattleModelList.length,
        itemBuilder: (context, index) {
          final item = logic.myBattleModelList[index];
          // 列表项
          return _buildListItem(item);
        });
  }

  /// 构建列表项
  Widget _buildListItem(MyBattleModel item) {
    DateTime parsedDate = DateTime.parse(item.matchTime ?? '');
    String formattedDate =
        DateFormat("yyyy-MM-dd EEE HH:mm").format(parsedDate);
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF191921), // 设置背景颜色
          borderRadius: BorderRadius.circular(8), // 设置圆角
        ),
        child: Stack(
          children: [
            // 主要内容
            Padding(
              padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 15.w),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                          width: 4.w,
                          height: 12.w,
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF
                                ],
                              ),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(2.r)))),
                      SizedBox(
                        width: 8.w,
                      ),
                      Text(
                        item.challengeTitle ?? "",
                        style: TextStyles.semiBold14,
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 15.w, bottom: 20.w),
                    color: Colours.color1AFFFFFF,
                    height: 1.w,
                  ),
                  InkWell(
                      onTap: () {
                        if (item.teamId == '0') {
                          AppPage.to(Routes.battleDetailPage,
                              arguments: {'challengeId': item.id});
                        } else {
                          AppPage.to(Routes.battleDetailTeamPage,
                              arguments: {'challengeId': item.id});
                        }
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: ScreenUtil().screenWidth - 60.w - 88.w,
                            child: Column(
                              children: [
                                _cellInfoWidget('球队：', item.teamName ?? ""),
                                SizedBox(
                                  height: 20.w,
                                ),
                                _cellInfoWidget('时间：', formattedDate),
                                SizedBox(
                                  height: 20.w,
                                ),
                                _cellInfoWidget(
                                    '费用：', item.challengeCostStr ?? ""),
                                SizedBox(
                                  height: 20.w,
                                ),
                                _cellInfoWidget(
                                    '强度：', item.challengeStrengthStr ?? ""),
                              ],
                            ),
                          ),
                          Container(
                            width: 68.w,
                            height: 28.w,
                            decoration: item.status == 0
                                ? BoxDecoration(
                                    border: Border.all(
                                      width: 1.w,
                                      color: Colors.transparent,
                                    ),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(32.r)),
                                  )
                                : BoxDecoration(
                                    border: Border.all(
                                      width: 1.w,
                                      color: Colours.color5C5C6E,
                                    ),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(32.r)),
                                  ),
                            child: item.status == 0
                                ? Container(
                                    decoration: BoxDecoration(
                                      gradient: const LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                      ),
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(32.r)),
                                    ),
                                    child: Container(
                                      margin: EdgeInsets.all(1.w),
                                      decoration: BoxDecoration(
                                        color: Colours.color191921,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(31.r)),
                                      ),
                                      child: Center(
                                        child: ShaderMask(
                                          shaderCallback: (bounds) =>
                                              const LinearGradient(
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            colors: [
                                              Colours.color7732ED,
                                              Colours.colorA555EF
                                            ],
                                          ).createShader(bounds),
                                          child: Text(
                                            '约战中',
                                            style:
                                                TextStyles.display12.copyWith(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  )
                                : Center(
                                    child: Text(
                                      item.status == 1 ? '已应战' : '已完成',
                                      style: TextStyles.display12.copyWith(
                                        color: Colours.color5C5C6E,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                          )
                        ],
                      )),
                  Container(
                    margin: EdgeInsets.only(top: 15.w, bottom: 20.w),
                    color: Colours.color1AFFFFFF,
                    height: 1.w,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      InkWell(
                        onTap: () async {
                          //分享到微信
                          InviteMiniPathModel inviteMiniPathModel =
                              InviteMiniPathModel(
                            appRawId: FluwxUtils.miniProgramUserName,
                            appId: FluwxUtils.appId,
                            path:
                                "pagesMatch/battle/battleDetail.html?battleId=${item.id}&teamId=${item.teamId}&battleType=2",
                            image: "",
                            icon: "",
                            title: "快来和我约战吧",
                          );
                          ByteData data = await rootBundle.load(
                              "assets/images/dialog_invitation.png"); //dialog_invitation
                          Uint8List imageBytes = data.buffer.asUint8List();

                          MyShareH5.shareMiniProgram(
                              inviteMiniPathModel, imageBytes);
                        },
                        child: WxAssets.images.icShare.image(),
                      ),
                      SizedBox(
                        width: 20.w,
                      ),
                      if (item.status == 0)
                        Row(
                          children: [
                            InkWell(
                              onTap: () => AppPage.to(Routes.createBattlePage,
                                      arguments: {'challengeModel': item})
                                  .then((v) {
                                if (v != null && v == true) {
                                  logic.onRefresh();
                                }
                              }),
                              child: WxAssets.images.editIcon.image(),
                            ),
                            SizedBox(
                              width: 20.w,
                            ),
                          ],
                        ),
                      InkWell(
                        onTap: () {
                          getMyDialog(
                            S.current.confirm_deletion_challenge,
                            S.current.sure,
                            content: S.current.confirm_deletion_dec_challenge,
                            () {
                              AppPage.back();
                              logic.deleteMatches(item.id ?? '');
                            },
                            isShowClose: false,
                            btnIsHorizontal: true,
                            btnText2: S.current.cancel,
                            onPressed2: () {
                              AppPage.back();
                            },
                          );
                        },
                        child: WxAssets.images.icDelete.image(),
                      ),
                      SizedBox(
                        width: 20.w,
                      ),
                      InkWell(
                        onTap: () => AppPage.to(Routes.purposeHistoryPage,
                            arguments: {'challengeId': item.id}),
                        child: Container(
                          width: 78.w,
                          height: 26.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              border:
                                  Border.all(color: Colors.white, width: 1.w),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(32.r))),
                          child: Text(
                            '意向记录',
                            style: TextStyles.display12
                                .copyWith(color: Colors.white),
                          ),
                        ),
                      )
                    ],
                  )
                ],
              ),
            ),
            // 右上角的全场/半场标签
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                width: 50.w,
                height: 20.w,
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        Colours.colorFFF9DC,
                        Colours.colorE4C8FF,
                        Colours.colorE5F3FF,
                      ],
                    ),
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomLeft: Radius.circular(8))),
                child: Center(
                  child: Text(
                    item.challengeType == 1 ? '全场' : '半场',
                    style: TextStyles.display10.copyWith(
                        color: Colours.color191921,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _cellInfoWidget(String leftStr, String rightStr) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: 48.w,
          child: Text(
            textAlign: TextAlign.left,
            leftStr,
            style: TextStyles.display14.copyWith(color: Colours.color5C5C6E),
          ),
        ),
        Text(
          rightStr,
          style: TextStyles.display14,
        )
      ],
    );
  }

  /// 显示约战类型选择底部弹窗
  void _showBattleTypeBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部指示条
              Container(
                width: 38.w,
                height: 3.w,
                margin: EdgeInsets.only(top: 6.w, bottom: 10.w),
                decoration: BoxDecoration(
                  color: Colours.color1AD8D8D8,
                  borderRadius: BorderRadius.circular(2.5.r),
                ),
              ),

              // 全场约战选项
              _buildBattleOption(
                context,
                title: '全场约战',
                onTap: () {
                  Navigator.pop(context);
                  AppPage.to(Routes.createBattlePage,
                      arguments: {'isHalf': false}).then((v) {
                    if (v != null && v == true) {
                      logic.onRefresh();
                    }
                  });
                },
              ),
              Container(
                height: 1,
                width: ScreenUtil().screenWidth - 30.w,
                color: Colours.color2F2F3B,
              ),
              // 半场约战选项
              _buildBattleOption(
                context,
                title: '半场约战',
                onTap: () {
                  Navigator.pop(context);
                  AppPage.to(Routes.createBattlePage,
                      arguments: {'isHalf': true}).then((v) {
                    if (v != null && v == true) {
                      logic.onRefresh();
                    }
                  });
                },
              ),

              // 底部安全区域
              SizedBox(height: ScreenUtil().bottomBarHeight + 20.w),
            ],
          ),
        );
      },
    );
  }

  /// 构建约战选项
  Widget _buildBattleOption(
    BuildContext context, {
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        child: Center(
          child: Text(
            title,
            style: TextStyles.semiBold,
          ),
        ),
      ),
    );
  }
}
