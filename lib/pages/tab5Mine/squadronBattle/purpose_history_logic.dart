import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/intention_model.dart';

class PurposeHistoryLogic extends GetxController {
  var intentionModelList = <IntentionModel>[].obs;
  String? challengeId;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  /// 是否正在加载数据
  bool _isLoading = false;
  var init = false.obs;
  var page = 1;
  var pageSize = 10;
  var totalRows = 0;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('challengeId')) {
      challengeId = Get.arguments['challengeId'];
    }
    onRefresh();
  }

  @override
  void onReady() {
    super.onReady();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await intentionList(false);
  }

  bool hasMore() {
    return intentionModelList.length < totalRows;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    await intentionList(true);
    // init.value = true;
  }

  //意向记录列表
  Future<void> intentionList(bool isRefresh) async {
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> request = {
      'id': challengeId,
      'limit': pageSize,
      'page': page
    };
    var res = await Api().get(ApiUrl.intentionList, queryParameters: request);
    _isLoading = false;
    init.value = true;
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      page += 1;
      final list = (res.data['list'] as List)
          .map((e) => IntentionModel.fromJson(e))
          .toList();
      totalRows = res.data["total"];
      if (isRefresh) {
        intentionModelList.value = list;
      } else {
        intentionModelList.addAll(list);
      }
    } else {
      if (isRefresh) {
        intentionModelList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }
}
