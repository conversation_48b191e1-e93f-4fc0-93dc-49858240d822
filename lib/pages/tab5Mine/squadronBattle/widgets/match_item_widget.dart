import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/matches_model.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/competition_match_mdel.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';

/// 比赛项目组件
class MatchItemWidget extends StatelessWidget {
  final String? matchTime;
  final List<String> courts;
  final int? status;
  final int leftTeamScore;
  final int rightTeamScore;
  final String? arenaName;
  final String leftTeamName;
  final String rightTeamName;
  final bool? leftWin;
  final bool? rightWin;
  final int? markStatus;
  final bool showStatus;
  final bool showStarIcon;
  final bool showArenaName;
  final String? matchId;
  const MatchItemWidget({
    super.key,
    this.matchTime,
    this.courts = const [],
    this.status,
    this.leftTeamScore = 0,
    this.rightTeamScore = 0,
    this.arenaName,
    this.leftTeamName = '',
    this.rightTeamName = '',
    this.leftWin,
    this.rightWin,
    this.markStatus,
    this.showStatus = true,
    this.showStarIcon = false,
    this.showArenaName = false,
    this.matchId,
  });

  /// Factory constructor for MatchesModel compatibility
  factory MatchItemWidget.fromMatchesModel({
    Key? key,
    required MatchesModel model,
    bool showStatus = true,
    bool showStarIcon = false,
    bool showArenaName = false,
  }) {
    return MatchItemWidget(
      key: key,
      matchTime: model.matchTime,
      courts: (model.courts ?? []).cast<String>(),
      status: model.status,
      arenaName: model.arenaName,
      leftTeamScore: model.leftTeamScore ?? 0,
      rightTeamScore: model.rightTeamScore ?? 0,
      leftTeamName: model.leftTeamName ?? '',
      rightTeamName: model.rightTeamName ?? '',
      leftWin: model.LeftWin,
      rightWin: model.rightWin,
      markStatus: model.markStatus,
      showStatus: showStatus,
      showStarIcon: showStarIcon,
      showArenaName: showArenaName,
      matchId: model.matchId,
    );
  }

  /// Factory constructor for Matches compatibility
  factory MatchItemWidget.fromMatches({
    Key? key,
    required Matches matchModel,
    bool showStatus = true,
    bool showStarIcon = false,
    bool showArenaName = false,
  }) {
    return MatchItemWidget(
      key: key,
      matchTime: matchModel.matchDateStr,
      courts: matchModel.courts,
      status: matchModel.status,
      arenaName: null,
      leftTeamScore: matchModel.leftScore,
      rightTeamScore: matchModel.rightScore,
      leftTeamName: matchModel.leftTeamName,
      rightTeamName: matchModel.rightTeamName,
      leftWin: null, // Matches model doesn't have win info
      rightWin: null, // Matches model doesn't have win info
      markStatus: matchModel.markStatus,
      showStatus: showStatus,
      showStarIcon: showStarIcon,
      showArenaName: false,
      matchId: matchModel.matchId,
    );
  }

  /// Factory constructor for CompetitionMatchModel compatibility
  factory MatchItemWidget.fromCompetitionMatchModel({
    Key? key,
    required CompetitionMatchModel model,
    bool showStatus = true,
    bool showStarIcon = false,
    bool showArenaName = false,
  }) {
    return MatchItemWidget(
      key: key,
      matchTime: model.startTime,
      arenaName: model.arenaName,
      courts: model.matchCourt != null ? [model.matchCourt!] : [],
      status: model.status,
      leftTeamScore: model.leftScore ?? 0,
      rightTeamScore: model.rightScore ?? 0,
      leftTeamName: model.leftTeamName ?? '',
      rightTeamName: model.rightTeamName ?? '',
      leftWin: null, // CompetitionMatchModel doesn't have win info
      rightWin: null, // CompetitionMatchModel doesn't have win info
      markStatus: model.markStatus,
      showStatus: showStatus,
      showStarIcon: showStarIcon,
      showArenaName: showArenaName,
      matchId: model.matchId.toString(),
    );
  }

  /// 根据状态返回对应的文本
  String _getStatusText(int? status) {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '比赛中';
      case 2:
        return '已结束';
      default:
        return '未开始';
    }
  }

  /// 根据状态返回对应的字体颜色
  Color _getStatusColor(int? status) {
    switch (status) {
      case 0:
        return Colors.white;
      case 1:
        return const Color(0xFF922BFF);
      case 2:
        return Colours.color5C5C6E;
      default:
        return Colors.white;
    }
  }

  String _getMarkStatusText(int? status) {
    switch (status) {
      case 0:
        return '待分析';
      case 1:
        return '分析中';
      case 2:
        return '已生成';
      default:
        return '待分析';
    }
  }

  @override
  Widget build(BuildContext context) {
    String formattedDate = "暂无";
    if ((matchTime ?? '') != "") {
      DateTime parsedDate = DateTime.parse(matchTime ?? '');
      formattedDate = DateFormat("MM.dd EEE HH:mm").format(parsedDate);
    }

    return Column(
      children: [
        if (showArenaName && (arenaName ?? '') != '')
          Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(bottom: 15.w),
            child: Text(
              arenaName ?? '',
              style: TextStyles.display12.copyWith(color: Colours.colorA8A8BC),
            ),
          ),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Text(
            formattedDate,
            style: TextStyles.display12
                .copyWith(fontFamily: 'DIN', color: Colours.color9E9E9E),
          ),
          Text(courts.join(' '),
              style: TextStyles.display12
                  .copyWith(fontFamily: 'DIN', color: Colours.color9E9E9E))
        ]),
        const SizedBox(
          height: 7,
        ),
        GestureDetector(
            onTap: () {
              if (status == 0 || status == 1) {
                AppPage.to(Routes.comparisonPage, arguments: {
                  "matchId": matchId, // "10000103", //
                });
              } else {
                AppPage.to(Routes.gameDetailsPage, arguments: matchId);
              }
            },
            child: Container(
              margin: EdgeInsets.only(bottom: 15.w),
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              width: double.infinity,
              height: (status == 0 && showStatus) ? 130.w : 90.w,
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: WxAssets.images.matchBg.provider(),
                      fit: BoxFit.fill)),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            leftTeamScore.toString(),
                            style: TextStyle(
                                color: leftWin ?? false
                                    ? Colours.color922BFF
                                    : Colors.white,
                                fontSize: 20.sp,
                                fontFamily: 'DIN',
                                fontWeight: FontWeight.bold),
                          ),
                          SizedBox(
                            height: 5.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              if (showStarIcon)
                                WxAssets.images.oneStarsIcon
                                    .image(width: 24.w, height: 24.w),
                              if (showStarIcon)
                                SizedBox(
                                  width: 5.w,
                                ),
                              SizedBox(
                                width: 130.w,
                                child: Text(
                                  leftTeamName,
                                  style: TextStyles.regular
                                      .copyWith(fontSize: 12.sp),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              )
                            ],
                          ),
                          status == 0 && showStatus
                              ? GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    // tap(1);
                                    AppPage.to(Routes.comparisonPage,
                                        arguments: {
                                          "matchId":
                                              matchId ?? '', // "10000103", //
                                        });
                                  },
                                  child: Container(
                                    height: 30.w,
                                    margin: EdgeInsets.only(top: 12.w),
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 10.w),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        color: Colours.white,
                                        gradient: const LinearGradient(
                                            colors: [
                                              Colours.colorFFECC1,
                                              Colours.colorE7CEFF,
                                              Colours.colorD1EAFF
                                            ],
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight),
                                        borderRadius:
                                            BorderRadius.circular(22.r)),
                                    child: Text(
                                      S.current.game_report_order,
                                      style: TextStyle(
                                          color: Colours.color000000,
                                          fontSize: 10.sp,
                                          height: 0,
                                          fontWeight: FontWeight.normal),
                                    ),
                                  ),
                                )
                              : const SizedBox(),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            rightTeamScore.toString(),
                            style: TextStyle(
                                color: rightWin ?? false
                                    ? Colours.color922BFF
                                    : Colors.white,
                                fontSize: 20.sp,
                                fontFamily: 'DIN',
                                fontWeight: FontWeight.bold),
                          ),
                          SizedBox(
                            height: 5.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              SizedBox(
                                width: 130.w,
                                child: Text(
                                  rightTeamName,
                                  style: TextStyles.regular
                                      .copyWith(fontSize: 12.sp),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.right,
                                ),
                              ),
                              if (showStarIcon)
                                SizedBox(
                                  width: 5.w,
                                ),
                              if (showStarIcon)
                                WxAssets.images.oneStarsIcon
                                    .image(width: 24.w, height: 24.w),
                            ],
                          ),
                          status == 0 && showStatus
                              ? GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    // tap(1);
                                    AppPage.to(Routes.comparisonPage,
                                        arguments: {
                                          "matchId":
                                              matchId ?? '', // "10000103", //
                                        });
                                  },
                                  child: Container(
                                    height: 30.w,
                                    margin: EdgeInsets.only(top: 12.w),
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 10.w),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        color: Colours.white,
                                        gradient: const LinearGradient(
                                            colors: [
                                              Colours.colorFFECC1,
                                              Colours.colorE7CEFF,
                                              Colours.colorD1EAFF
                                            ],
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight),
                                        borderRadius:
                                            BorderRadius.circular(22.r)),
                                    child: RichText(
                                      text: TextSpan(
                                          text: "立即获取",
                                          style: TextStyle(
                                              color: Colours.color000000,
                                              fontSize: 10.sp,
                                              height: 0,
                                              fontWeight: FontWeight.normal),
                                          children: <InlineSpan>[
                                            TextSpan(
                                                text: "(享优惠)",
                                                style: TextStyle(
                                                    color: Colours.color922BFF,
                                                    fontSize: 10.sp,
                                                    height: 0,
                                                    fontWeight:
                                                        FontWeight.normal)),
                                          ]),
                                    ),
                                  ),
                                )
                              : const SizedBox(),
                        ],
                      ),
                    ],
                  ),
                  Positioned(
                    top: 28.w,
                    child: showStatus
                        ? Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                width: 1,
                                color: Colors.transparent,
                              ),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(32.r)),
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [
                                    Color(0xCC8244FF),
                                    Color(0x148E7AAA),
                                  ],
                                ),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(32.r)),
                              ),
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 15.w),
                                height: 24.w,
                                margin: EdgeInsets.all(1.w),
                                decoration: BoxDecoration(
                                  color: Colours.color191921,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(31.r)),
                                ),
                                child: Center(
                                  child: Text(
                                    '${_getStatusText(status)}${status != 0 ? '（${_getMarkStatusText(markStatus)}）' : ''}',
                                    style: TextStyles.display10.copyWith(
                                      color: _getStatusColor(status),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ))
                        : const SizedBox(),
                  )
                ],
              ),
            ))
      ],
    );
  }
}
