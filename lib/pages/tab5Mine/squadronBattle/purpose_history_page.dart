import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/intention_model.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/purpose_history_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的约战列表
class PurposeHistoryPage extends StatelessWidget {
  PurposeHistoryPage({super.key});

  final logic = Get.put(PurposeHistoryLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('意向记录'),
      ),
      body: Obx(() {
        return NotificationListener(
            onNotification: (ScrollNotification note) {
              if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                logic.loadMore();
              }
              return true;
            },
            child: RefreshIndicator(
              onRefresh: logic.onRefresh,
              child: Container(
                color: Colours.bg_color,
                child: logic.init.value
                    ? (logic.intentionModelList.isEmpty
                        ? _emptyView(context)
                        : _listView(context))
                    : buildLoad(),
              ),
            ));
      }),
    );
  }

  Widget _emptyView(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: constraints.maxHeight - 40,
              child: myNoDataView(
                context,
                msg: '暂时还没有人报名哦，请耐心等待～',
                imagewidget: WxAssets.images.purposeEmpty.image(),
              ),
            ));
      },
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 15.w),
        itemCount: logic.intentionModelList.length,
        itemBuilder: (context, index) {
          final item = logic.intentionModelList[index];
          // 列表项
          return _buildListItem(item);
        });
  }

  /// 构建列表项
  Widget _buildListItem(IntentionModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.w),
      decoration: BoxDecoration(
        color: const Color(0xFF191921), // 设置背景颜色
        borderRadius: BorderRadius.circular(8.r), // 设置圆角
      ),
      child: Column(
        children: [
          _cellInfoWidget('姓名', item.userName ?? "",
              detailTap: () => AppPage.to(Routes.sssy)),
          SizedBox(
            height: 20.w,
          ),
          _cellInfoWidget('电话', item.contactPhone ?? '',
              showRightWidget: false),
          SizedBox(
            height: 20.w,
          ),
          _cellInfoWidget('球队', item.teamName ?? "", detailTap: () {
            log("message!!!!!${item.teamId}");
            if ((item.teamId ?? '').isEmpty || item.teamId == "0") {
              WxLoading.showToast('该用户未选择任何球队');
              return;
            }
            AppPage.to(Routes.teamInfoPage, arguments: {
              'teamId': item.teamId,
            });
          }),
          SizedBox(
            height: 20.w,
          ),
          _cellInfoWidget('备注信息', item.remark ?? "",
              showRightWidget: false, maxLine: 2),
          SizedBox(
            height: 20.w,
          ),
          _cellInfoWidget('提交时间', item.createdTime ?? "", contactTap: () {
            if (item.contactPhone != null && item.contactPhone!.isNotEmpty) {
              Get.dialog(
                CustomAlertDialog(
                  title: '拨打电话 ${item.contactPhone}',
                  onPressed: () {
                    AppPage.back();
                    Utils.phoneTelURL(item.contactPhone!);
                  },
                ),
              );
            } else {
              WxLoading.showToast('暂无发布者联系方式');
            }
          }),
        ],
      ),
    );
  }

  Widget _cellInfoWidget(String leftStr, String rightStr,
      {bool showRightWidget = true,
      Function? contactTap,
      Function? detailTap,
      int maxLine = 1}) {
    return InkWell(
        onTap: () {
          if (detailTap != null) {
            detailTap();
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  SizedBox(
                    width: 60.w,
                    child: Text(
                      textAlign: TextAlign.left,
                      leftStr,
                      style: TextStyles.display14
                          .copyWith(color: Colours.color5C5C6E),
                    ),
                  ),
                  SizedBox(
                    width: 20.w,
                  ),
                  Expanded(
                    child: Text(
                      rightStr,
                      style: TextStyles.display14,
                      maxLines: maxLine,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            if (leftStr == '提交时间' && showRightWidget)
              InkWell(
                onTap: () {
                  if (contactTap != null) {
                    contactTap();
                  }
                },
                child: Container(
                  width: 72.w,
                  height: 28.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.all(Radius.circular(14.r))),
                  child: Text(
                    '联系他',
                    style: TextStyles.textSize12.copyWith(color: Colors.white),
                  ),
                ),
              ),
            if (leftStr != '提交时间' && showRightWidget)
              Icon(
                Icons.arrow_forward_ios,
                size: 14.w,
                color: Colours.colorA8A8BC,
              )
          ],
        ));
  }
}
