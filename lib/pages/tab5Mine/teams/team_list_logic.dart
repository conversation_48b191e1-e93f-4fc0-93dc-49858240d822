import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/team_list_model.dart';

class TeamListLogic extends GetxController with WidgetsBindingObserver {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  SwiperController swiperController = SwiperController();
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <TeamListModel>[].obs;
  //是否是选择球队
  var isSelectTeam = false;
  //数据列表
  var dataTeamList = <TeamListModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('selectTeam')) {
      isSelectTeam = Get.arguments['selectTeam'];
    }
    getdataList(isLoad: false, controller: refreshController);
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      // 'page': dataFag["page"] ?? 1,
      // 'limit': 20,
    };
    var res = await Api().get(ApiUrl.myTeamsList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data ?? [];
      List<TeamListModel> modelList =
          list.map((e) => TeamListModel.fromJson(e)).toList();
      log("zzzzzz12removeAt-${res.data}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }

      // // 使用 where 方法过滤列表
      List<TeamListModel> dataTeamList2 = dataList.where((value) {
        return value.leader == true;
      }).toList();
      dataTeamList.assignAll(dataTeamList2);
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
