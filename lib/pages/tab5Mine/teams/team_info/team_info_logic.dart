import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/team_home_model.dart';
import 'package:shoot_z/network/model/team_member_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item1/team_info_item_logic1.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/list_items/item2/team_info_item_logic2.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

class TeamInfoLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  TabController? tabController;
  var tabNameList = [
    S.current.home,
    S.current.data,
    S.current.competitions,
    S.current.schedule,
    S.current.highlights,
  ].obs;
  var dialogDatalist = [
    {
      "img": "dialog_team_info1.png",
      "name": S.current.team_info_diolog_tips1,
      "index": 0,
      "id": "0"
    },
    {
      "img": "dialog_team_info2.png",
      "name": S.current.team_info_diolog_tips2,
      "index": 1,
      "id": "1"
    },
    {
      "img": "dialog_team_info3.png",
      "name": S.current.team_info_diolog_tips3,
      "index": 2,
      "id": "2"
    },
    {
      "img": "dialog_team_info4.png",
      "name": S.current.team_info_diolog_tips4,
      "index": 3,
      "id": "3"
    },
    {
      "img": "dialog_team_info5.png",
      "name": S.current.team_info_diolog_tips5,
      "index": 4,
      "id": "4"
    },
  ].obs;

  var dataFag = {
    "isFrist": true,
  }.obs;
  var tabbarIndex = 0.obs;
  var teamId = "".obs;
  var teamHomeModel = TeamHomeModel().obs;
  var teamMemberList = <TeamMemberModel>[].obs;
  var yiJiaoUserId = "".obs;
  var yiJiaoUserName = "".obs;
  final logic1 = Get.put(TeamInfoItemLogic1());
  final logic2 = Get.put(TeamInfoItemLogic2());
  StreamSubscription? subscription;

  @override
  void onInit() {
    super.onInit();
    teamId.value = Get.arguments["teamId"];
    tabController = TabController(length: 5, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
      },
    );
    if (Get.arguments != null && Get.arguments.containsKey('index')) {
      switchTab(Get.arguments["index"]);
      tabController?.animateTo(Get.arguments["index"]);
    }
    getTeamInfo();
  }

  void switchTab(index) {
    tabbarIndex.value = index;
  }

  @override
  void onReady() {
    super.onReady();
    subscription = BusUtils.instance.on((action) {
      if (action.key == EventBusKey.auditApplyTeam) {
        getTeamInfo();
      } else if (action.key == EventBusKey.teamChangeTab) {
        tabController?.animateTo(4);
      }
    });
  }

  //获得球馆主页详情
  getTeamInfo() async {
    Map<String, dynamic> param = {
      //  'teamId': teamId.value,
    };
    var url = await ApiUrl.getTeamSummary(teamId.value);
    var res = await Api().get(url, queryParameters: param);
    if (res.isSuccessful()) {
      log("scoreHistory2=${jsonEncode(res.data["scoreHistory"])}");
      teamHomeModel.value = TeamHomeModel.fromJson(res.data);
      teamHomeModel.refresh();
      logic1.setTeamHomeModel(teamHomeModel.value);
    } else {
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
  }

  Future<void> getDialogOnClick(String id, int index) async {
    switch (id) {
      case "0": //编辑我的球员资料
        Get.back();
        AppPage.to(Routes.updateTeamMemberPage,
            arguments: {"teamId": teamId.value}).then((v) {
          getTeamInfo();
        });
        break;
      case "1": //编辑球队资料
        Get.back();
        AppPage.to(Routes.addTeamPage, arguments: {"teamId": teamId.value})
            .then((v) {
          getTeamInfo();
        });
        break;
      case "2": //设为主队
        Get.back();
        putTeamDefault();
        break;
      case "3": //移交队长
        Get.back();
        if (teamMemberList.isEmpty) {
          await getTeamMembers();
        } else {
          getChangeTeamLeaderDialog(Get.context!);
        }
        break;
      case "4": //退出球队 解散球队
        Get.back();
        if (teamHomeModel.value.leader == true &&
            (teamMemberList.length) >= 2) {
          //移交队长
          getMyDialog(
            S.current.team_info_diolog_tips5,
            S.current.team_info_diolog_tips4,
            content: S.current.team_info_diolog_tips9,
            () {
              AppPage.back();
              getChangeTeamLeaderDialog(Get.context!);
            },
            isShowClose: false,
            btnIsHorizontal: true,
            btnText2: S.current.cancel,
            onPressed2: () {
              AppPage.back();
            },
          );
        } else if ( //teamHomeModel.value.leader == true &&
            (teamMemberList.length) <= 1) {
          //解散球队
          getMyDialog(
            S.current.team_info_diolog_tips7,
            S.current.team_info_diolog_tips7,
            content: S.current.team_info_diolog_tips8,
            () {
              AppPage.back();
              postTeamQuit();
            },
            isShowClose: false,
            btnIsHorizontal: true,
            btnText2: S.current.cancel,
            onPressed2: () {
              AppPage.back();
            },
          );
        } else {
          //退出球队
        }

        break;
    }
  }

  //设为主队
  putTeamDefault() async {
    Map<String, dynamic> param = {
      'teamId': teamId.value,
    };
    WxLoading.show();
    var url = await ApiUrl.putTeamDefault(teamId.value);
    var res = await Api().PUT(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast(S.current.modification_successful);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //移交队长
  putTeamTransfer(String targetUserId) async {
    Map<String, dynamic> param = {
      'teamId': teamId.value,
      'targetUserId': targetUserId,
    };
    WxLoading.show();
    var url = await ApiUrl.putTeamTransfer(teamId.value, targetUserId);
    var res = await Api().PUT(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      getTeamInfo();
      WxLoading.showToast(S.current.modification_successful);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //退出球队
  postTeamQuit() async {
    Map<String, dynamic> param = {
      'teamId': teamId.value,
    };
    WxLoading.show();
    var url = await ApiUrl.postTeamQuit(teamId.value);
    var res = await Api().post(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      // getTeamInfo();
      WxLoading.showToast(S.current.Successful);
      AppPage.back();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //加入球队
  postTeamJoin() async {
    Map<String, dynamic> param = {
      'teamId': teamId.value,
    };
    WxLoading.show();
    var url = await ApiUrl.postTeamJoin(teamId.value);
    var res = await Api().post(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      getTeamInfo();
      WxLoading.showToast(S.current.Successful);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //查询球队队员
  getTeamMembers() async {
    Map<String, dynamic> param = {
      'teamId': teamId.value,
    };
    WxLoading.show();
    var url = await ApiUrl.getTeamMembers(teamId.value);
    var res = await Api().get(url, queryParameters: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      List list = res.data ?? [];
      List<TeamMemberModel> modelList =
          list.map((e) => TeamMemberModel.fromJson(e)).toList();
      teamMemberList.assignAll(modelList);
      getChangeTeamLeaderDialog(Get.context!);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //移交队长
  void getChangeTeamLeaderDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 632,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 18.w, bottom: 20.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.team_info_diolog_tips4,
                      style: TextStyles.medium.copyWith(fontSize: 16.sp),
                    )),
                Expanded(
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      SizedBox(
                        height: 580.w,
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            GridView.builder(
                                scrollDirection: Axis.vertical,
                                // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                shrinkWrap: true,
                                physics:
                                    const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 4,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                  childAspectRatio: 75 / 130,
                                ),
                                padding:
                                    EdgeInsets.only(bottom: 70.w, top: 0.w),
                                itemCount: teamMemberList.length,
                                itemBuilder: (context, index) {
                                  return Obx(() {
                                    return GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        yiJiaoUserId.value =
                                            teamMemberList[index].userId ?? "";
                                        yiJiaoUserName.value =
                                            teamMemberList[index].userName ??
                                                "";
                                        yiJiaoUserId.refresh();
                                      },
                                      child: Column(
                                        children: [
                                          Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              MyImage(
                                                teamMemberList[index]
                                                        .userPhoto ??
                                                    '',
                                                //  holderImg: "home/index/df_banner_top",
                                                fit: BoxFit.fill,
                                                width: 75.w,
                                                height: 100.w,
                                                isAssetImage: false,
                                                // errorImg: "home/index/df_banner_top"
                                                radius: 4.r,
                                              ),
                                              if (yiJiaoUserId.value ==
                                                  teamMemberList[index].userId)
                                                Container(
                                                  width: 75.w,
                                                  height: 100.w,
                                                  alignment: Alignment.center,
                                                  decoration:
                                                      const BoxDecoration(
                                                          color: Colours
                                                              .color80000000),
                                                  child: Container(
                                                    width: 20.w,
                                                    height: 20.w,
                                                    margin: EdgeInsets.only(
                                                        right: 8.w,
                                                        bottom: 3.w,
                                                        top: 8.w),
                                                    child: const Icon(
                                                      Icons.check,
                                                      color: Colours.white,
                                                      size: 20,
                                                    ),
                                                  ),
                                                )
                                            ],
                                          ),
                                          SizedBox(
                                            height: 10.w,
                                          ),
                                          Center(
                                            child: Text(
                                              teamMemberList[index].userName ??
                                                  "",
                                              maxLines: 1,
                                              textAlign: TextAlign.center,
                                              style: TextStyles.regular
                                                  .copyWith(
                                                      fontSize: 12.sp,
                                                      color:
                                                          Colours.color9393A5),
                                            ),
                                          )
                                        ],
                                      ),
                                    );
                                  });
                                }),
                          ],
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.only(bottom: 25.w, top: 10.w),
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () async {
                            if (yiJiaoUserId.value == "" ||
                                yiJiaoUserId.value ==
                                    UserManager.instance.user?.userId) {
                              WxLoading.showToast(
                                  S.current.team_info_diolog_tips6);
                              return;
                            }
                            Get.back();

                            //加入球队
                            getMyDialog(
                              S.current.Transfer_team,
                              S.current.sure,
                              content: S.current.team_info_diolog_tips12(
                                  yiJiaoUserName.value),
                              () {
                                AppPage.back();
                                putTeamTransfer(yiJiaoUserId.value);
                              },
                              isShowClose: false,
                              btnIsHorizontal: true,
                              btnText2: S.current.cancel,
                              onPressed2: () {
                                AppPage.back();
                              },
                            );
                          },
                          child: Container(
                            height: 46.w,
                            width: double.infinity,
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(left: 20.w, right: 20.w),
                            decoration: BoxDecoration(
                              color: Colours.color282735,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(28.r)),
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.color7732ED,
                                  Colours.colorA555EF
                                ],
                                begin: Alignment.bottomLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: Text(
                              S.current.sure,
                              style: TextStyles.display16
                                  .copyWith(fontSize: 16.sp),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
              ],
            ),
          );
        });
      },
    );
  }
}
