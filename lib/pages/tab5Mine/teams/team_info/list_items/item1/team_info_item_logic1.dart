import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/team_home_model.dart';
import 'package:shoot_z/network/model/team_player_rank_model.dart';
import 'package:shoot_z/widgets/ImageDotPainter.dart';
import 'package:ui_packages/ui_packages.dart';

class TeamInfoItemLogic1 extends GetxController {
  RefreshController refreshController1 =
      RefreshController(initialRefresh: false);
  RefreshController refreshController2 =
      RefreshController(initialRefresh: false);
  RefreshController refreshController3 =
      RefreshController(initialRefresh: false);
  var teamHomeModel = TeamHomeModel().obs;
  var isFrist = true.obs;
  var dataFag = {
    "isFrist1": true,
    "page1": 1,
    "isFrist2": true,
    "page2": 1,
    "isFrist3": true,
    "page3": 1,
  }.obs;
  var teamId = "".obs;
  var teamRankTitle = <String>[
    S.current.Player_scoring_list,
    S.current.Team_rebounding_list,
    S.current.Team_assist_list
  ];
  var teamRankIndex = 0.obs;
  var teamMemberList1 = <TeamPlayerRankModel>[].obs;
  var teamMemberList2 = <TeamPlayerRankModel>[].obs;
  var teamMemberList3 = <TeamPlayerRankModel>[].obs;
  var lineChartData = LineChartData().obs;
  var _selectedPointIndex = 555.obs;
  @override
  void onInit() {
    super.onInit();
    teamId.value = Get.arguments["teamId"];
  }

  @override
  void onReady() {
    super.onReady();
    getTeamMembers(1);
  }

  setTeamHomeModel(teamHomeModel1) {
    teamHomeModel.value = teamHomeModel1;
    teamHomeModel.value.scoreHistory;
    // dataPoints.addAll(teamHomeModel.value.scoreHistory!!);
    lineChartData.value = LineChartData(
      // minY: minY,
      // maxY: maxY,
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false, //画竖线
        getDrawingHorizontalLine: (value) => FlLine(
          color: Colors.grey.withOpacity(0.3),
          strokeWidth: 1,
        ),
      ),
      titlesData: FlTitlesData(
        show: true,
        bottomTitles: AxisTitles(
          // axisNameSize:
          //     (teamHomeModel.value.scoreHistory?.length ?? 0).toDouble(),
          sideTitles: SideTitles(
            showTitles: true,
            interval: (teamHomeModel.value.scoreHistory?.length ?? 0) < 10
                ? 1
                : (teamHomeModel.value.scoreHistory?.length ?? 0) < 20
                    ? 2
                    : (teamHomeModel.value.scoreHistory?.length ?? 0) < 30
                        ? 3
                        : (teamHomeModel.value.scoreHistory?.length ?? 0) % 10,
            reservedSize: 30.w,
            getTitlesWidget: (value, meta) {
              // 将X轴的数值索引转换为日期
              final date =
                  teamHomeModel.value.scoreHistory?[value.toInt()]?.date ?? "";
              return Padding(
                padding: EdgeInsets.only(top: 12.w),
                child: Text(
                  date, //.toDotFormat()
                  style: TextStyles.regular.copyWith(fontSize: 10.sp),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 40.w,
            getTitlesWidget: (value, meta) {
              // 显示Y轴数值
              return Padding(
                padding: EdgeInsets.only(right: 5.w),
                child: Text(
                  value.toInt().toString(),
                  style: TextStyles.regular.copyWith(fontSize: 10.sp),
                  textAlign: TextAlign.right,
                ),
              );
            },
          ),
        ),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        rightTitles:
            const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: const Color(0xff37434d), width: 1),
      ),
      lineTouchData: LineTouchData(
        handleBuiltInTouches: true,
        getTouchedSpotIndicator: defaultTouchedIndicators,
        touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) => Colours.color15151D,
            tooltipBorder:
                const BorderSide(color: Colours.color2F2F3B, width: 1),
            tooltipMargin: 16,
            tooltipPadding:
                const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
            getTooltipItems: defaultLineTooltipItem),
        touchCallback: (event, response) {
          if (event is FlTapUpEvent) {
            _selectedPointIndex.value =
                response?.lineBarSpots?.first.spotIndex ?? 0;
          }
        },
      ),
      lineBarsData: [
        LineChartBarData(
          spots: teamHomeModel.value.scoreHistory!.asMap().entries.map((entry) {
            // 将数据点转换为FLSpot(X索引, Y值)
            final index = entry.key;
            final point = entry.value;
            return FlSpot(index.toDouble(), (point!.score ?? 0).toDouble());
          }).toList(),
          isCurved: true, // 使用曲线
          color: Colours.color922BFF,
          barWidth: 2,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true, //chart_point
            getDotPainter: (spot, percent, barData, index) {
              // 为选中点创建特殊样式
              if (index == _selectedPointIndex.value) {
                return PreloadedImageDotPainter(
                  assetPath: 'assets/images/chart_point.png',
                  sizeScale: 1.0, // 可选：缩放图像尺寸
                  fallbackColor: Colours.color922BFF, // 可选：备用颜色
                );
              }
              // 默认点样式
              return FlDotCirclePainter(
                color: Colours.color922BFF,
                radius: 4,
              );
            },
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: [
                Colours.color922BFF.withOpacity(0.3),
                Colours.color922BFF.withOpacity(0.0)
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ],
    );

    teamHomeModel.refresh();
    if (isFrist.value) {
      isFrist.value = false;
      isFrist.refresh();
    }
  }

  //查询球队队员排行
  getTeamMembers(
    int type, {
    isLoad = false,
  }) async {
    var page = 1;

    switch (type) {
      case 1:
        if (!(dataFag["isFrist1"] as bool) && !isLoad) {
          return;
        }
        page = dataFag["page1"] as int;
        break;
      case 2:
        if (!(dataFag["isFrist2"] as bool) && !isLoad) {
          return;
        }
        page = dataFag["page2"] as int;
        break;
      case 3:
        if (!(dataFag["isFrist3"] as bool) && !isLoad) {
          return;
        }
        page = dataFag["page3"] as int;
        break;
    }
    if (!isLoad) {
      page = 1;
    }
    Map<String, dynamic> param = {
      'limit': 10,
      'page': page,
      'type': type, //1.得分 2.篮板 3.助攻
    };
    //WxLoading.show();
    var url = await ApiUrl.getTeamPlayerRank(teamId.value);
    var res = await Api().get(url, queryParameters: param);
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<TeamPlayerRankModel> modelList =
          list.map((e) => TeamPlayerRankModel.fromJson(e)).toList();
      switch (type) {
        case 1:
          dataFag["page1"] = page + 1;
          if (isLoad) {
            teamMemberList1.addAll(modelList);
            teamMemberList1.refresh();
            if (modelList.length < 10) {
              refreshController1.loadNoData();
            } else {
              refreshController1.loadComplete();
            }
          } else {
            refreshController1.resetNoData();
            teamMemberList1.assignAll(modelList);
            refreshController1.refreshCompleted();
          }
          if (dataFag["isFrist1"] as bool) {
            dataFag["isFrist1"] = false;
            dataFag.refresh();
          }
          break;
        case 2:
          dataFag["page2"] = page + 1;
          if (isLoad) {
            teamMemberList2.addAll(modelList);
            teamMemberList2.refresh();
            if (modelList.length < 10) {
              refreshController2.loadNoData();
            } else {
              refreshController2.loadComplete();
            }
          } else {
            refreshController2.resetNoData();
            teamMemberList2.assignAll(modelList);
            refreshController2.refreshCompleted();
          }
          if (dataFag["isFrist2"] as bool) {
            dataFag["isFrist2"] = false;
            dataFag.refresh();
          }
          break;
        case 3:
          dataFag["page3"] = page + 1;
          if (isLoad) {
            teamMemberList3.addAll(modelList);
            teamMemberList3.refresh();
            if (modelList.length < 10) {
              refreshController3.loadNoData();
            } else {
              refreshController3.loadComplete();
            }
          } else {
            refreshController3.resetNoData();
            teamMemberList3.assignAll(modelList);
            refreshController3.refreshCompleted();
          }
          if (dataFag["isFrist3"] as bool) {
            dataFag["isFrist3"] = false;
            dataFag.refresh();
          }
          break;
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }
}

extension DateFormatter on String {
  String toDotFormat() {
    // 支持多种格式
    final formats = [
      RegExp(r'^(\d{1,2})[/](\d{1,2})$'), // 10/05
      RegExp(r'^(\d{2})\.(\d{2})$'), // 10.05
      RegExp(r'^(\d{1,2})\-(\d{1,2})$') // 10-05
    ];

    for (final format in formats) {
      final match = format.firstMatch(this);
      if (match != null) {
        final month = int.parse(match.group(1)!);
        final day = int.parse(match.group(2)!);
        return '$month.$day';
      }
    }

    return this;
  }
}
