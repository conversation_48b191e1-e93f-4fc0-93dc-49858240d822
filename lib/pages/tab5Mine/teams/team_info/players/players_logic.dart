import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/team_players2_model.dart';
import 'package:shoot_z/utils/event_bus.dart';

class PlayersLogic extends GetxController with WidgetsBindingObserver {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <TeamPlayers2Model>[].obs;
  var teamId = "";
  @override
  void onInit() {
    super.onInit();
    teamId = Get.arguments["teamId"];
    getdataList(isLoad: false, controller: refreshController);
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'page': dataFag["page"] ?? 1,
      'limit': 20,
    };
    var url = await ApiUrl.getTeamPlayers(teamId);
    var res = await Api().get(url, queryParameters: param);
    log("${param}");
    if (res.isSuccessful()) {
      List list = res.data ?? [];
      List<TeamPlayers2Model> modelList =
          list.map((e) => TeamPlayers2Model.fromJson(e)).toList();
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

//审核入队审核
  postTeamApplyAudit(int id, int type) async {
    Map<String, dynamic> param = {
      "id": id, "status": type //2 通过 3 拒绝
    };
    WxLoading.show();
    var url = await ApiUrl.postTeamApplyAudit(teamId);
    var res = await Api().post(url, data: param);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      // getTeamInfo();
      getdataList(isLoad: false, controller: refreshController);
      if (type == 2) {
        BusUtils.instance.fire(EventAction(key: EventBusKey.auditApplyTeam));
      } //auditApplyTeam

      WxLoading.showToast(
          type == 2 ? S.current.Already_agreed : S.current.Rejected);
    } else {
      WxLoading.showToast(res.message);
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
