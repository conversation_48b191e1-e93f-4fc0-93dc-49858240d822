import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/team_players2_model.dart';
import 'package:shoot_z/pages/tab5Mine/teams/team_info/players/players_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 二级页面->我的球队列表 球队主页  入队审核列表
class PlayersPage extends StatelessWidget {
  PlayersPage({super.key});

  final logic = Get.put(PlayersLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.players_list),
      ),
      body: _listWidget(context),
    );
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
          controller: logic.refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () {
            logic.getdataList(
                isLoad: false, controller: logic.refreshController);
          },
          onLoading: () {
            logic.getdataList(
                isLoad: true, controller: logic.refreshController);
          },
          physics: const AlwaysScrollableScrollPhysics(),
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? myNoDataView(context,
                      msg: S.current.no_players,
                      imagewidget: WxAssets.images.noDataPeople
                          .image(width: 180.w, height: 120.w),
                      height: 2,
                      margin: EdgeInsets.only(bottom: 140.w))
                  : ListView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      padding: EdgeInsets.only(bottom: 40.w),
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: logic.dataList.length,
                      itemBuilder: (context, position) {
                        return _listItemWidget(logic.dataList[position]);
                      }));
    });
  }

  /// 构建列表项
  Widget _listItemWidget(TeamPlayers2Model item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r), color: Colours.color191921),
      child: Row(
        children: [
          MyImage(
            item.userPhoto ?? "",
            width: 46.w,
            height: 46.w,
            radius: 23.r,
            placeholderImage: "my_team_head4.png",
            errorImage: "my_team_head4.png",
          ),
          SizedBox(
            width: 10.w,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      "${item.userName}",
                      style: TextStyles.regular.copyWith(
                          fontSize: 16.sp,
                          color: Colours.white,
                          fontWeight: FontWeight.w600),
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    if (item.leader == 1)
                      Container(
                        margin: EdgeInsets.only(left: 5.w),
                        padding: EdgeInsets.symmetric(
                            horizontal: 3.w, vertical: 2.w),
                        decoration: BoxDecoration(
                            color: Colours.colorFF661A,
                            borderRadius: BorderRadius.circular(10.r)),
                        child: Text(
                          S.current.leader,
                          style: TextStyles.regular.copyWith(fontSize: 8.sp),
                          maxLines: 1,
                        ),
                      ),
                    if (item.me == true)
                      Container(
                        margin: EdgeInsets.only(left: 5.w),
                        padding: EdgeInsets.symmetric(
                            horizontal: 3.w, vertical: 2.w),
                        decoration: BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.circular(10.r)),
                        child: Text(
                          S.current.me,
                          style: TextStyles.regular.copyWith(fontSize: 8.sp),
                          maxLines: 1,
                        ),
                      )
                  ],
                ),
                SizedBox(
                  height: 13.w,
                ),
                Text(
                  "${(item.height == null || item.height == "") ? 0 : item.height ?? 0}CM | ${(item.weight == null || item.weight == "") ? 0 : item.weight ?? 0}KG | 全能选手",
                  style: TextStyles.regular
                      .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
