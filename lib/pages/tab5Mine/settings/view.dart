import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:utils_package/utils_package.dart';

import '../../../generated/l10n.dart';
import '../../../routes/app.dart';
import '../../../routes/route.dart';
import '../../../widgets/update_version.dart';
import '../mine_item_view.dart';
import 'logic.dart';

class SettingsPage extends StatelessWidget {
  SettingsPage({super.key});
  final logic = Get.put(SettingLogic());
  final state = Get.find<SettingLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.settings),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(left: 15, right: 15, top: 2),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.only(left: 20, right: 15),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colours.color191921,
                ),
                child: Column(
                  children: [
                    MineItemView(
                        text: S.current.about_us,
                        onTap: () => AppPage.to(Routes.aboutPage)),
                    Obx(() {
                      return MineItemView(
                          text: S.current.current_version,
                          rightWidget: Container(
                            height: 48.w,
                            alignment: Alignment.centerRight,
                            child: Row(
                              children: [
                                if (state.isCanUpdateVersion.value)
                                  Container(
                                    width: 6.w,
                                    height: 6.w,
                                    margin: EdgeInsets.only(right: 6.w),
                                    decoration: BoxDecoration(
                                        color: Colours.red,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(3.w))),
                                  ),
                                Text(
                                  "V ${WxAppInfoUtils.instance.version}",
                                  style: TextStyles.regular
                                      .copyWith(fontSize: 14.sp),
                                ),
                                SizedBox(
                                  width: 2.w,
                                ),
                              ],
                            ),
                          ),
                          onTap: () {
                            UpdateVersion.getVersion(context, type: 1);
                          });
                    }),
                    MineItemView(
                        text: S.current.deregister_account,
                        onTap: logic.deregisterAccount),
                    Obx(() {
                      return MineItemView(
                          text: S.current.recommendation,
                          onTap: () async {
                            logic.state.isOpenRecommendation.value =
                                !logic.state.isOpenRecommendation.value;
                            await WxStorage.instance.setString(
                                "isOpenRecommendation",
                                logic.state.isOpenRecommendation.value
                                    ? "1"
                                    : "0");
                          },
                          rightWidget: Container(
                            width: 40.w,
                            height: 30.w,
                            alignment: Alignment.centerLeft,
                            child: MyImage(
                              logic.state.isOpenRecommendation.value
                                  ? "switch_on.png"
                                  : "switch_off.png",
                              fit: BoxFit.fitWidth,
                              bgColor: Colors.transparent,
                              isAssetImage: true,
                              radius: 0.r,
                              width: 40.w,
                            ),
                          ));
                    }),
                  ],
                ),
              ),
              const Spacer(),
              _logout(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _logout() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 43),
      child: WxButton(
        backgroundColor: Colors.black.withOpacity(0.4),
        height: 50,
        text: S.current.log_out,
        textStyle: TextStyles.regular.copyWith(color: Colours.colorA54040),
        onPressed: logic.logout,
      ),
    );
  }
}
