import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:ui_packages/ui_packages.dart';

// ignore: must_be_immutable
class MineItemView extends StatelessWidget {
  final String? icon;
  final String text;
  final GestureTapCallback onTap;
  Widget? rightWidget;
  MineItemView(
      {super.key,
      this.icon,
      required this.text,
      this.rightWidget,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: SizedBox(
        width: double.infinity,
        height: 48.w,
        child: Row(
          children: [
            if (icon != null)
              AssetGenImage('assets/images/$icon.png')
                  .image(width: 32.w, height: 32.w),
            if (icon != null)
              SizedBox(
                width: 11.w,
              ),
            Text(
              text,
              style: TextStyles.semiBold14,
            ),
            const Expanded(child: SizedBox.shrink()),
            if (rightWidget != null) rightWidget ?? const SizedBox(),
            if (rightWidget == null)
              WxAssets.images.icArrowRight.image(
                  width: 14.w,
                  height: 14.w,
                  color: Colors.white.withOpacity(0.5)),
          ],
        ),
      ),
    );
  }
}
