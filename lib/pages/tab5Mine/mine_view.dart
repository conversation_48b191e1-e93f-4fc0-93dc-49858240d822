import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/inappwebview/router.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab5Mine/mine_item_view.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../generated/l10n.dart';
import 'mine_logic.dart';

class MinePage extends StatefulWidget {
  const MinePage({super.key});

  @override
  State<MinePage> createState() => _MinePageState();
}

class _MinePageState extends State<MinePage>
    with AutomaticKeepAliveClientMixin {
  final MineLogic logic = Get.put(MineLogic());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      logic.subscribe(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: 15.w),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: WxAssets.images.mineBg.provider(),
                      fit: BoxFit.fill)),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Column(
                  children: [
                    SizedBox(height: ScreenUtil().statusBarHeight),
                    Obx(() => UserManager.instance.isLoginObs.value
                        ? _loginHead(context)
                        : _notLogged(context)),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                children: [
                  SizedBox(
                    height: 15.w,
                  ),
                  _banner(context),
                  SizedBox(
                    height: 15.w,
                  ),
                  //赛事和集锦
                  _sssy(context),
                  SizedBox(
                    height: 15.w,
                  ),
                  //我的订单 我的球队
                  _myOrderAndTeam(context),
                  SizedBox(
                    height: 15.w,
                  ),
                  _kf(context),
                  SizedBox(
                    height: 35.w,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _banner(BuildContext context) {
    final user = UserManager.instance.userInfo;
    return GestureDetector(
      onTap: () {
        AppPage.to(Routes.pointsPage);
      },
      child: Container(
        padding: EdgeInsets.all(15.w),
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage("assets/images/mine_points_bg.png"),
                fit: BoxFit.fill)),
        child: Row(
          children: [
            MyImage(
              "mine_points.png",
              width: 50.w,
              height: 50.w,
              isAssetImage: true,
            ),
            SizedBox(
              width: 10.w,
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      "${S.current.integral}：",
                      style: TextStyles.display14,
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    Obx(
                      () => Text(
                        "${UserManager.instance.userInfo.value?.point}",
                        style: TextStyles.titleSemiBold16.copyWith(
                            fontSize: 18.sp, color: Colours.color922BFF),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 2.w,
                ),
                Text(
                  S.current.points_tips,
                  style: TextStyles.display12
                      .copyWith(fontSize: 10.sp, color: Colours.color5C5C6E),
                )
              ],
            ),
            const Spacer(),
            Container(
              height: 32.w,
              width: 90.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: WxAssets.images.icMineBtnBg.provider(),
                      fit: BoxFit.fill)),
              child: Obx(
                () => Text(
                  (user.value != null && user.value!.isSignToday)
                      ? S.current.signed_in
                      : S.current.go_signed,
                  style: TextStyles.titleMedium18
                      .copyWith(fontSize: 14.w, color: Colours.color0F0F16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _loginHead(BuildContext context) {
    return Obx(
      () {
        final user = UserManager.instance.userInfo.value;
        return Column(
          children: [
            Row(
              children: [
                Container(
                  width: 70.w, // 圆形图片的宽度
                  height: 70.w, // 圆形图片的高度
                  margin: EdgeInsets.only(top: 30.w),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle, // 圆形
                    image: DecorationImage(
                      image: CachedNetworkImageProvider(user?.avatar ?? ''),
                      fit: BoxFit.cover,
                    ),
                    border: Border.all(
                      color: Colors.white, // 边框颜色
                      width: 1.0, // 边框宽度
                    ),
                  ),
                ),
                SizedBox(
                  width: 15.w,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        height: 30.w,
                      ),
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              user?.userName ?? '',
                              style: TextStyles.titleSemiBold16
                                  .copyWith(fontSize: 16.sp),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          SizedBox(
                            width: 9.w,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10.w,
                      ),
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 11.w),
                            // 设置内边距
                            height: 20.w,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Colours.color282735, // 背景颜色（可选）
                              borderRadius: BorderRadius.circular(10.w), // 圆角半径
                            ),
                            child: Text(
                              user?.gender == 1
                                  ? S.current.male
                                  : S.current.female,
                              style: TextStyles.display12
                                  .copyWith(color: Colours.color5C5C6E),
                            ),
                          ),
                          SizedBox(
                            width: 5.w,
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 11.w),
                            // 设置内边距
                            height: 20.w,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Colours.color282735, // 背景颜色（可选）
                              borderRadius: BorderRadius.circular(10.w), // 圆角半径
                            ),
                            child: Text(
                              S.current.age(user?.age ?? 0),
                              style: TextStyles.display12
                                  .copyWith(color: Colours.color5C5C6E),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        AppPage.to(Routes.messageTypePage, needLogin: true);
                      },
                      child: Container(
                        width: 53.w,
                        padding: EdgeInsets.only(right: 5.w),
                        child: Stack(
                          alignment: Alignment.centerRight,
                          children: [
                            WxAssets.images.homeMessage
                                .image(width: 20.w, height: 20.w),
                            Positioned(
                              top: 1,
                              child: Obx(() => UserManager
                                          .instance
                                          .messageHasUnreadModel
                                          .value
                                          ?.hasUnread ==
                                      true
                                  ? Container(
                                      width: 6.w,
                                      height: 6.w,
                                      decoration: BoxDecoration(
                                          color: Colours.red,
                                          borderRadius:
                                              BorderRadius.circular(3.r)),
                                    )
                                  : const SizedBox()),
                            )
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () => AppPage.to(Routes.modifyInfo),
                        child: Container(
                            margin: EdgeInsets.only(top: 18.w),
                            padding: EdgeInsets.symmetric(
                                horizontal: 15.w, vertical: 9.w),
                            decoration: BoxDecoration(
                                image: DecorationImage(
                                    image:
                                        WxAssets.images.mineUpdate.provider(),
                                    fit: BoxFit.fill)),
                            alignment: Alignment.center,
                            child: Row(
                              children: [
                                WxAssets.images.mineUpdate2
                                    .image(width: 12.w, height: 12.w),
                                SizedBox(
                                  width: 5.w,
                                ),
                                Text(
                                  S.current.edit_information,
                                  style: TextStyles.regular
                                      .copyWith(fontSize: 12.sp),
                                ),
                              ],
                            ))),
                  ],
                ),
              ],
            ),
            if ((user?.vipLevel ?? 0) == 0)
              GestureDetector(
                onTap: () {
                  AppPage.to(Routes.vipPage);
                },
                child: Container(
                  margin: EdgeInsets.only(top: 15.w),
                  alignment: Alignment.centerLeft,
                  height: 70.w,
                  padding: EdgeInsets.only(left: 15.w),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.mineVip1.provider(),
                          fit: BoxFit.fill)),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              S.current.get_vip,
                              style: TextStyles.regular.copyWith(
                                  color: Colours.colorffFFBB68,
                                  fontSize: 14.sp),
                            ),
                            SizedBox(
                              height: 12.w,
                            ),
                            Text(
                              S.current.get_vip_tips,
                              style: TextStyles.regular.copyWith(
                                  color: Colours.white, fontSize: 12.sp),
                            ),
                          ],
                        ),
                      ),
                      Container(
                          margin: EdgeInsets.only(right: 15.w),
                          padding: EdgeInsets.symmetric(
                              horizontal: 15.w, vertical: 8.w),
                          decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  Colours.colorffF8DB9C,
                                  Colours.colorffFFCF51,
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(20.r)),
                          child: Text(
                            S.current.Open_immediately,
                            style: TextStyles.regular.copyWith(
                              color: Colours.color4C311F,
                              fontSize: 12.sp,
                            ),
                          )),
                    ],
                  ),
                ),
              ),
            if ((user?.vipLevel ?? 0) == 1)
              GestureDetector(
                onTap: () {
                  AppPage.to(Routes.vipPage);
                },
                child: Container(
                  margin: EdgeInsets.only(top: 15.w),
                  alignment: Alignment.centerLeft,
                  height: 70.w,
                  padding: EdgeInsets.only(left: 15.w),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.mineVip2.provider(),
                          fit: BoxFit.fill)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          WxAssets.images.mineVip.image(
                              width: 40.w, height: 16.w, fit: BoxFit.fill),
                          SizedBox(
                            width: 20.w,
                          ),
                          WxAssets.images.mineVipArrow.image(
                            width: 7.w,
                            height: 15.w,
                            color: Colours.white,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 12.w,
                      ),
                      Text(
                        "${S.current.vip_expire_time}：${user?.vipExpireDate}",
                        style: TextStyles.regular
                            .copyWith(color: Colours.white, fontSize: 12.sp),
                      ),
                    ],
                  ),
                ),
              ),
            if ((user?.vipLevel ?? 0) == 2)
              GestureDetector(
                onTap: () {
                  AppPage.to(Routes.vipPage);
                },
                child: Container(
                  margin: EdgeInsets.only(top: 15.w),
                  alignment: Alignment.centerLeft,
                  height: 70.w,
                  padding: EdgeInsets.only(left: 15.w),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: WxAssets.images.mineSvip.provider(),
                          fit: BoxFit.fill)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          WxAssets.images.mineSvipIcon
                              .image(width: 56.w, height: 16.w),
                          SizedBox(
                            width: 20.w,
                          ),
                          WxAssets.images.mineVipArrow
                              .image(width: 7.w, height: 15.w),
                        ],
                      ),
                      SizedBox(
                        height: 12.w,
                      ),
                      Text(
                        "${S.current.vip_expire_time}：${user?.vipExpireDate}",
                        style: TextStyles.regular.copyWith(
                            color: Colours.color922BFF, fontSize: 12.sp),
                      ),
                    ],
                  ),
                ),
              )
          ],
        );
      },
    );
  }

  Widget _notLogged(BuildContext context) {
    return Container(
        height: 129.w,
        padding: EdgeInsets.symmetric(horizontal: 6.w),
        alignment: Alignment.centerLeft,
        child: GestureDetector(
          behavior: HitTestBehavior.opaque, // 扩展点击范围至整个 Row
          onTap: () => AppPage.to(Routes.login),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 70.w, // 圆形图片的宽度
                height: 70.w, // 圆形图片的高度
                decoration: BoxDecoration(
                  shape: BoxShape.circle, // 圆形
                  image: DecorationImage(
                    image: WxAssets.images.icMineWdl.provider(),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              SizedBox(
                width: 15.w,
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    S.current.click_to_log_in,
                    style: TextStyles.titleMedium18.copyWith(fontSize: 20.sp),
                  ),
                  SizedBox(
                    height: 8.w,
                  ),
                  Text(
                    S.current.get_your_highlights,
                    style: TextStyles.display14
                        .copyWith(color: Colors.white.withOpacity(0.6)),
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  Widget _sssy(BuildContext context) {
    return GestureDetector(
      onTap: () => AppPage.to(Routes.careerHighlightsHomePage, needLogin: true),
      child: Column(
        children: [
          buildRowTitleWidget("生涯与集锦", height: 51.w, padding: EdgeInsets.zero,
              rightOnTap: () {
            AppPage.to(Routes.careerHighlightsHomePage, needLogin: true);
          }),
          Obx(() {
            final summary = UserManager.instance.summaryModel.value;
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                gameWidget(S.current.score, '${summary?.score ?? 0}'),
                gameWidget(S.current.rebound, '${summary?.rebound ?? 0}'),
                gameWidget(S.current.assist, '${summary?.assist ?? 0}'),
                gameWidget(S.current.shotRate, '${summary?.shotRate ?? 0}'),
                gameWidget(S.current.sum_game, '${summary?.totalMatch ?? 0}'),
              ],
            );
          })
        ],
      ),
    );
  }

  Widget gameWidget(String name, String value) {
    return Container(
      width: 61.w,
      padding: EdgeInsets.only(left: 10.w, top: 15.w, bottom: 15.w),
      decoration: const BoxDecoration(
          image: DecorationImage(
              image: AssetImage("assets/images/mine_game.png"),
              fit: BoxFit.fill)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            style: TextStyles.regular
                .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
          ),
          SizedBox(
            height: 12.w,
          ),
          Text(
            value,
            style: TextStyles.regular
                .copyWith(fontSize: 14.sp, color: Colours.color922BFF),
          ),
        ],
      ),
    );
  }

  Widget _kf(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 4.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colours.color191921,
      ),
      child: Column(
        children: [
          MineItemView(
              icon: 'mine_card',
              text: "卡券",
              onTap: () {
                // }
                if (UserManager.instance.isLogin) {
                  AppPage.to(Routes.couponsPage);
                } else {
                  AppPage.to(Routes.login).then((onValue) async {
                    await Future.delayed(const Duration(milliseconds: 500));
                    if (UserManager.instance.isLogin) {
                      AppPage.to(Routes.couponsPage);
                    } else {
                      WxLoading.showToast(S.current.please_login);
                    }
                  });
                }
              }),
          const Divider(
            color: Colours.color99292937,
            height: 10,
          ),
          MineItemView(
              icon: 'mine_kefu',
              text: S.current.feedback,
              onTap: () {
                var url = "";
                if (const String.fromEnvironment('env', defaultValue: 'dev') !=
                    'pro') {
                  url =
                      "https://idev.shootz.tech/kf/?userId=${UserManager.instance.userInfo.value?.userId ?? ""}";
                } else {
                  url =
                      "https://i.shootz.tech/kf/?userId=${UserManager.instance.userInfo.value?.userId ?? ""}";
                }

                //测试 https://idev.shootz.tech/kf 正式 https://i.shootz.tech/kf
                WebviewRouter router = WebviewRouter(
                    url: url,
                    showNavigationBar: true,
                    needBaseHttp: false,
                    title: S.current.feedback);
                AppPage.to(Routes.webviewh5, arguments: router);
              }),
          const Divider(
            color: Colours.color99292937,
            height: 10,
          ),
          MineItemView(
              icon: 'mine_tuijian',
              text: S.current.recommend_to_friend,
              onTap: () => AppPage.to(Routes.inviteCodePage)),
          const Divider(
            color: Colours.color99292937,
            height: 10,
          ),
          MineItemView(
              icon: 'mine_join',
              text: S.current.join_us,
              onTap: () => AppPage.to(Routes.kfPage)),
          const Divider(
            color: Colours.color99292937,
            height: 10,
          ),
          MineItemView(
              icon: 'mine_set',
              text: S.current.settings,
              onTap: () => AppPage.to(Routes.settingsPage)),
        ],
      ),
    );
  }

  Widget _myOrderAndTeam(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 4.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colours.color191921,
      ),
      child: Column(
        children: [
          MineItemView(
              icon: 'mine_team',
              text: S.current.my_team,
              onTap: () {
                AppPage.to(Routes.teamListPage);
              }),
          const Divider(
            color: Colours.color99292937,
            height: 10,
          ),
          MineItemView(
              icon: 'mine_order',
              text: S.current.my_order,
              onTap: () {
                AppPage.to(Routes.ordersPage);
              }),
          // MineItemView(
          //     icon: 'ic_my_order',
          //     text: "球员报告",
          //     onTap: () {
          //       if (UserManager.instance.isLogin) {
          //         AppPage.to(Routes.playerReportPage,
          //             arguments: {"teamId": "", "playerId": "", "matchId": ""});
          //       } else {
          //         AppPage.to(Routes.login).then((onValue) async {
          //           await Future.delayed(const Duration(milliseconds: 500));
          //           if (UserManager.instance.isLogin) {
          //             AppPage.to(Routes.playerReportPage, arguments: {
          //               "teamId": "",
          //               "playerId": "",
          //               "matchId": ""
          //             });
          //           } else {
          //             WxLoading.showToast(S.current.please_login);
          //           }
          //         });
          //       }
          //     }),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
