import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/message_list_model.dart';

class MessageListLogic extends GetxController with WidgetsBindingObserver {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  SwiperController swiperController = SwiperController();
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;

  //数据列表
  var dataList = <MessageListModel>[].obs;
  var messageName = "".obs;
  var messageType = "".obs;
  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null && Get.arguments.containsKey('messageType')) {
      messageType.value = Get.arguments['messageType'];
    }
    if (Get.arguments != null && Get.arguments.containsKey('messageName')) {
      messageName.value = Get.arguments['messageName'];
    }
    getdataList(isLoad: false, controller: refreshController);
  }

  @override
  void onReady() {
    super.onReady();
  }

  //获得最新列表
  getdataList({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'pageIndex': dataFag["page"] ?? 1,
      'pageSize': 20,
      "messageType": messageType.value
    };
    log("getMessageList2-${param}");
    var res = await Api().get(ApiUrl.getMessageList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["result"] ?? [];
      List<MessageListModel> modelList =
          list.map((e) => MessageListModel.fromJson(e)).toList();
      log("getMessageList2-${res.data}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
