import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/message_type_list_model.dart';
import 'package:shoot_z/pages/tab5Mine/message/message_type/message_type_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 消息 消息分类列表
class MessageTypePage extends StatelessWidget {
  MessageTypePage({super.key});

  final logic = Get.put(MessageTypeLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MyAppBar(
        title: Text("我的消息"),
      ),
      body: _listWidget(context),
    );
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
          controller: logic.refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () {
            logic.getdataList(
                isLoad: false, controller: logic.refreshController);
          },
          physics: const AlwaysScrollableScrollPhysics(),
          child: (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? myNoDataView(
                      context,
                      msg: S.current.No_data_available,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 150.w, height: 89.w),
                    )
                  : ListView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      padding: EdgeInsets.only(bottom: 40.w),
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: logic.dataList.length,
                      itemBuilder: (context, position) {
                        return _listItemWidget(logic.dataList[position]);
                      }));
    });
  }

  /// 构建列表项
  Widget _listItemWidget(MessageTypeListModel messageTypeListModel) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        AppPage.to(Routes.messageListPage, arguments: {
          "messageType": messageTypeListModel.typeId.toString(),
          "messageName": messageTypeListModel.typeName
        });
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        padding: EdgeInsets.only(
            left: 15.w,
            right: 15.w,
            bottom: 15.w,
            top: ((messageTypeListModel.unreadNum ?? 0) > 0) ? 9.w : 15.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: Colours.color191921),
        child: Column(
          children: [
            if ((messageTypeListModel.unreadNum ?? 0) > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    width: 6.w,
                    height: 6.w,
                    decoration: BoxDecoration(
                        color: Colours.red,
                        borderRadius: BorderRadius.circular(3.r)),
                  ),
                ],
              ),
            Row(
              children: [
                (messageTypeListModel.typeName == "系统消息")
                    ? WxAssets.images.messageSystem
                        .image(width: 40.w, height: 40.w)
                    : WxAssets.images.messageGuanfang
                        .image(width: 40.w, height: 40.w),
                SizedBox(
                  width: 12.w,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        messageTypeListModel.typeName ?? "",
                        style: TextStyles.regular.copyWith(
                            fontSize: 14.sp,
                            color: Colours.white,
                            fontWeight: FontWeight.w600),
                      ),
                      SizedBox(
                        height: 13.w,
                      ),
                      Text(
                        messageTypeListModel.latestTitle ?? "",
                        style: TextStyles.regular.copyWith(
                            fontSize: 12.sp, color: Colours.colorA8A8BC),
                      ),
                    ],
                  ),
                ),
                MyImage("ic_arrow_right.png",
                    width: 14.w,
                    height: 14.w,
                    isAssetImage: true,
                    imageColor: Colours.color9393A5),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
