import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/message/message_info/message_info_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 我的球队列表->新增球队
class MessageInfoPage extends StatelessWidget {
  MessageInfoPage({super.key});

  final logic = Get.put(MessageInfoLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Obx(() {
          return Text(logic.messageName.value);
        }),
      ),
      body: _createTeamWidget(context),
    );
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Obx(() {
      return logic.isFrist.value
          ? buildLoad()
          : logic.messageListModel.value.messageId == null
              ? myNoDataView(
                  context,
                  msg: S.current.No_data_available,
                  imagewidget: WxAssets.images.icGameNo
                      .image(width: 150.w, height: 150.w),
                )
              : _teamInfoWidget(context);
    });
  }

  Widget _teamInfoWidget(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
            padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  logic.messageListModel.value.title ?? "",
                  maxLines: 20,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyles.regular.copyWith(
                      fontSize: 14.sp, color: Colours.white, height: 1.2),
                ),
                SizedBox(
                  height: 15.w,
                ),
                Row(
                  children: [
                    WxAssets.images.messageJiqi
                        .image(width: 26.w, height: 26.w),
                    SizedBox(
                      width: 6.w,
                    ),
                    Flexible(
                      child: Text(
                        logic.messageListModel.value.sendName ?? "",
                        style: TextStyles.regular
                            .copyWith(fontSize: 14.sp, color: Colours.white),
                      ),
                    ),
                    SizedBox(
                      width: 6.w,
                    ),
                    Text(
                      logic.messageListModel.value.sendTime ?? "",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp,
                          color: Colours.color5C5C6E,
                          height: 1.2),
                    ),
                  ],
                ),
                if ((logic.messageListModel.value.coverImg ?? "") != "")
                  SizedBox(
                    height: 21.w,
                  ),
                if ((logic.messageListModel.value.coverImg ?? "") != "")
                  MyImage(
                    logic.messageListModel.value.coverImg ?? "",
                    width: double.infinity,
                    height: 172.w,
                    radius: 8.r,
                    errorImage: "error_image_width.png",
                    placeholderImage: "error_image_width.png",
                  ),
                SizedBox(
                  height: 20.w,
                ),
                Text(
                  (logic.messageListModel.value.content ?? ""),
                  style: TextStyles.regular.copyWith(
                      fontSize: 14.sp, color: Colours.white, height: 1.2),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
