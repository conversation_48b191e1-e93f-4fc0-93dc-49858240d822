import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/career_highlights_home_logic.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/my_career_page.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/my_highlights_page.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:ui_packages/ui_packages.dart';

///生涯与集锦主页
class CareerHighlightsHomePage extends StatelessWidget {
  CareerHighlightsHomePage({super.key});

  final logic = Get.put(CareerHighlightsHomeLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: _createDetailWidget(context));
  }

  _createDetailWidget(BuildContext context) {
    return Column(
      children: [
        _topBarWidget(context),
        // TabBarView 内容区域
        Expanded(
          child: Tab<PERSON><PERSON><PERSON>ie<PERSON>(
            controller: logic.tabController,
            children: [
              MyCareerPage(),
              MyHighlightsPage(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _topBarWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Column(
        children: [
          // 顶部导航栏
          Container(
            height: 50.w,
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 60.w,
                  padding: EdgeInsets.only(left: 8.w, right: 10.w, top: 6.w),
                  child: IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios,
                      size: 20.w,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      AppPage.back();
                    },
                  ),
                ),
                // TabBar
                Container(
                  // color: Colors.red,
                  width: 220.w,
                  alignment: Alignment.bottomCenter,
                  child: TabBar(
                    controller: logic.tabController,
                    indicator: const UnderlineTabIndicator(
                      borderSide: BorderSide.none,
                      insets: EdgeInsets.zero,
                    ),
                    labelColor: Colors.transparent,
                    unselectedLabelColor: Colors.transparent,
                    labelStyle: TextStyles.titleSemiBold16,
                    dividerColor: Colors.transparent,
                    dividerHeight: 0,
                    unselectedLabelStyle:
                        TextStyles.regular.copyWith(fontSize: 14.sp),
                    overlayColor: WidgetStateProperty.all(Colors.transparent),
                    tabs: [
                      Tab(
                        child: Column(
                          // mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Obx(() => logic.currentTabIndex.value == 0
                                ? ShaderMask(
                                    shaderCallback: (bounds) =>
                                        const LinearGradient(
                                      colors: [
                                        Colours.colorFFF9DC,
                                        Colours.colorE4C8FF,
                                        Colours.colorE5F3FF,
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ).createShader(bounds),
                                    child: Text(
                                      logic.tab1Name,
                                      style:
                                          TextStyles.titleSemiBold16.copyWith(
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                : Text(
                                    logic.tab1Name,
                                    style: TextStyles.regular.copyWith(
                                      fontSize: 14.sp,
                                      color: Colours.color5C5C6E,
                                    ),
                                  )),
                            Obx(() => logic.currentTabIndex.value == 0
                                ? WxAssets.images.imgCheckIn2.image()
                                : SizedBox(height: 10.w)),
                          ],
                        ),
                      ),
                      Tab(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          // mainAxisSize: MainAxisSize.min,
                          children: [
                            Obx(() => logic.currentTabIndex.value == 1
                                ? ShaderMask(
                                    shaderCallback: (bounds) =>
                                        const LinearGradient(
                                      colors: [
                                        Colours.colorFFF9DC,
                                        Colours.colorE4C8FF,
                                        Colours.colorE5F3FF,
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ).createShader(bounds),
                                    child: Text(
                                      logic.tab2Name,
                                      style:
                                          TextStyles.titleSemiBold16.copyWith(
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                : Text(
                                    logic.tab2Name,
                                    style: TextStyles.regular.copyWith(
                                      fontSize: 14.sp,
                                      color: Colours.color5C5C6E,
                                    ),
                                  )),
                            Obx(() => logic.currentTabIndex.value == 1
                                ? WxAssets.images.imgCheckIn2.image()
                                : SizedBox(height: 10.w)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 60.w,
                  padding: EdgeInsets.only(left: 10.w, right: 8.w),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
