import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/model/career_match_model.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/model/mine_career_model.dart';

class MyCareerLogic extends GetxController {
  var isLoading = false.obs;
  var init = false.obs;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  //数据列表
  var dataList = <CareerMatchModel>[].obs;
  //数据列表
  var careerModel = Rxn<MineCareerModel>();
  @override
  void onInit() {
    super.onInit();
    onRefresh();
    onRefreshCareer(isLoad: false, controller: refreshController);
  }

  @override
  void onReady() {
    super.onReady();
  }

  Future<void> onRefresh() async {
    isLoading.value = true;
    Map<String, dynamic> param = {'userId': UserManager.instance.user?.userId};
    var res = await Api().get(ApiUrl.myCareerData, queryParameters: param);
    init.value = true;
    isLoading.value = false;
    if (res.isSuccessful()) {
      careerModel.value = MineCareerModel.fromJson(res.data);
    }
  }

  Future<void> onRefreshCareer({
    isLoad = true,
    required RefreshController controller,
  }) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }
    Map<String, dynamic> param = {
      'pageIndex': dataFag["page"] ?? 1,
      'pageSize': 20,
      'userId': '462119'
    };
    var res =
        await Api().get(ApiUrl.getMatchCareerList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data['result'] ?? [];
      List<CareerMatchModel> modelList =
          list.map((e) => CareerMatchModel.fromJson(e)).toList();
      cc.log("message!!!!!!!!!$list");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          controller.loadNoData();
          //  controller.loadComplete();
        } else {
          controller.loadComplete();
        }
      } else {
        controller.resetNoData();
        dataList.assignAll(modelList);
        controller.refreshCompleted();
      }
    } else {
      controller.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      refresh();
    }
  }
}
