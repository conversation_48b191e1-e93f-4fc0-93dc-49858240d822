import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/model/career_match_model.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/my_career_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

/// 我的生涯页面
class MyCareerPage extends StatelessWidget {
  MyCareerPage({super.key});

  final logic = Get.put(MyCareerLogic());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return logic.careerModel.value == null
          ? buildLoad()
          : SmartRefresher(
              controller: logic.refreshController,
              footer: buildFooter(),
              header: buildClassicHeader(),
              enablePullDown: false,
              enablePullUp: true,
              onRefresh: () {
                logic.onRefreshCareer(
                    isLoad: false, controller: logic.refreshController);
              },
              onLoading: () {
                logic.onRefreshCareer(
                    isLoad: true, controller: logic.refreshController);
              },
              physics: const AlwaysScrollableScrollPhysics(),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.all(15.w),
                      height: 90.w,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                              image: WxAssets.images.careerBg.provider(),
                              fit: BoxFit.fill)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              child: Row(
                            children: [
                              Container(
                                width: 60.w,
                                height: 60.w,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 1.w,
                                  ),
                                ),
                                child: ClipOval(
                                  child: Image.network(
                                    logic.careerModel.value?.avatar ?? '',
                                    width: 60.w,
                                    height: 60.w,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 15.w,
                              ),
                              Expanded(
                                  child: SizedBox(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      maxLines: 1,
                                      logic.careerModel.value?.realName == ""
                                          ? '未填写名称'
                                          : logic.careerModel.value?.realName ??
                                              '未填写名称',
                                      style: TextStyles.titleSemiBold16,
                                    ),
                                    SizedBox(
                                      height: 15.w,
                                    ),
                                    Text(
                                      maxLines: 1,
                                      '用户昵称:(${logic.careerModel.value?.name ?? ''})',
                                      style: TextStyles.display12
                                          .copyWith(color: Colours.colorA8A8BC),
                                    ),
                                  ],
                                ),
                              )),
                            ],
                          )),
                          GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () => AppPage.to(Routes.modifyInfo),
                              child: Container(
                                  height: 30.w,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 15.w, vertical: 9.w),
                                  decoration: BoxDecoration(
                                      image: DecorationImage(
                                          image: WxAssets.images.mineUpdate
                                              .provider(),
                                          fit: BoxFit.fill)),
                                  alignment: Alignment.center,
                                  child: Row(
                                    children: [
                                      WxAssets.images.mineUpdate2
                                          .image(width: 12.w, height: 12.w),
                                      SizedBox(
                                        width: 5.w,
                                      ),
                                      Text(
                                        S.current.complete_information,
                                        style: TextStyles.regular
                                            .copyWith(fontSize: 12.sp),
                                      ),
                                    ],
                                  )))
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Wrap(
                      spacing: 15.w,
                      runSpacing: 15.w,
                      children: [
                        gameWidget(
                            S.current.location,
                            _getPositionStr(
                                logic.careerModel.value?.position ?? 0)),
                        gameWidget(S.current.height,
                            '${logic.careerModel.value?.height == '' ? '0' : logic.careerModel.value?.height}CM'),
                        gameWidget(S.current.weight,
                            '${logic.careerModel.value?.weight == '' ? '0' : logic.careerModel.value?.weight}KG'),
                        gameWidget(S.current.team_tag8,
                            '${logic.careerModel.value?.avgScore == '' ? '0' : logic.careerModel.value?.avgScore}'),
                        gameWidget(S.current.average_field_goal_rate,
                            '${logic.careerModel.value?.avgShoot == '' ? '0' : logic.careerModel.value?.avgShoot}'),
                        gameWidget(S.current.team_tag11,
                            '${logic.careerModel.value?.avgRate == '' ? '0' : logic.careerModel.value?.avgRate}%'),
                      ],
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      child: TextWithIcon(title: S.current.the_affiliated_team),
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    Container(
                        height: 90.w,
                        padding: EdgeInsets.symmetric(
                            horizontal: 7.5.w, vertical: 7.5.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: Colours.color191921),
                        child: (logic.careerModel.value?.teams ?? []).isNotEmpty
                            ? ListView.builder(
                                scrollDirection: Axis.horizontal, // 设置为水平滚动
                                itemCount:
                                    logic.careerModel.value?.teams?.length ??
                                        0, // 图片数量
                                itemBuilder: (context, index) {
                                  return Padding(
                                      padding: EdgeInsets.all(7.5.w),
                                      child: InkWell(
                                        onTap: () => AppPage.to(
                                            Routes.teamInfoPage,
                                            arguments: {
                                              'teamId': logic.careerModel.value
                                                      ?.teams![index]?.teamId ??
                                                  '0'
                                            }),
                                        child: Container(
                                          width: 60.w,
                                          height: 60.w,
                                          padding: EdgeInsets.all(10.w),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8.r),
                                            color: Colours.color0F0F16,
                                          ),
                                          child: Container(
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(20.r),
                                                color: (logic
                                                                .careerModel
                                                                .value
                                                                ?.teams![index]
                                                                ?.teamLogo ??
                                                            '')
                                                        .isEmpty
                                                    ? Colours.red
                                                    : null,
                                              ),
                                              child: (logic
                                                              .careerModel
                                                              .value
                                                              ?.teams![index]
                                                              ?.teamLogo ??
                                                          '')
                                                      .isEmpty
                                                  ? Text(
                                                      logic
                                                              .careerModel
                                                              .value
                                                              ?.teams![index]
                                                              ?.teamName ??
                                                          '',
                                                      maxLines: 1,
                                                      textAlign:
                                                          TextAlign.center,
                                                      style:
                                                          TextStyles.semiBold14,
                                                    )
                                                  : MyImage(
                                                      logic
                                                              .careerModel
                                                              .value
                                                              ?.teams![index]
                                                              ?.teamLogo ??
                                                          '',
                                                      width: 40.w,
                                                      height: 40.w,
                                                      radius: 20.w,
                                                      fit: BoxFit.fill,
                                                    )),
                                        ),
                                      ));
                                },
                              )
                            : Center(
                                child: Text(
                                  S.current.no_team_for_now,
                                  style: TextStyles.textDark,
                                ),
                              )),
                    SizedBox(
                      height: 15.w,
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      child: TextWithIcon(title: S.current.player_career),
                    ),
                    SizedBox(
                      height: 15.w,
                    ),
                    _buildListContent(context)
                  ],
                ).marginSymmetric(horizontal: 15.w, vertical: 15.w),
              ),
            );
    });
  }

  /// 构建列表内容
  Widget _buildListContent(BuildContext context) {
    return Obx(() {
      if (logic.dataFag["isFrist"] as bool) {
        return buildLoad();
      } else if (logic.dataList.isEmpty) {
        return myNoDataView(
          context,
          msg: S.current.no_matches_yet,
          imagewidget: WxAssets.images.battleEmptyIcon.image(),
        );
      } else {
        return Column(
          children: [
            ...logic.dataList.map((item) => _listItemWidget(item)),
            SizedBox(height: 40.w),
          ],
        );
      }
    });
  }

  /// 构建列表项
  Widget _listItemWidget(CareerMatchModel item) {
    return InkWell(
        onTap: () {
          AppPage.to(Routes.playerReportPage, arguments: {
            "teamId": item.teamId ?? '0',
            "playerId": item.playerId,
            "matchId": item.matchId
          });
        },
        child: Container(
          height: 140.w,
          padding: EdgeInsets.all(15.w),
          margin: EdgeInsets.only(bottom: 15.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              color: Colours.color191921),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      '${item.matchDate ?? ''}  ${item.arenaName ?? ''}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyles.display12
                          .copyWith(color: Colours.color5C5C6E),
                    ),
                  ),
                  SizedBox(
                    width: 50.w,
                  ),
                  Text(
                    (item.courts ?? []).join('、'),
                    style: TextStyles.display12,
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  MyImage(
                    item.photo ?? "",
                    width: 60.w,
                    height: 60.w,
                    radius: 8.r,
                    placeholderImage: "my_team_head4.png",
                    errorImage: "my_team_head4.png",
                  ),
                  matchItem(S.current.score, '${item.shootScore}'),
                  matchItem(S.current.shotRate, '${item.shootRate}%'),
                  matchItem(S.current.detail_video, '${item.fragmentCount}'),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                      child: Text(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    "${item.teamName}",
                    style: TextStyles.display12.copyWith(color: Colours.white),
                  )),
                  SizedBox(
                    width: 50.w,
                  ),
                  Text(
                    "*数据来源于球秀AI",
                    style: TextStyles.display12
                        .copyWith(color: Colours.color5C5C6E),
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  _getPositionStr(int position) {
    switch (position) {
      case 1:
        return '控球后卫';
      case 2:
        return '得分后卫';
      case 3:
        return '小前锋';
      case 4:
        return '大前锋';
      case 5:
        return '中锋';
      case 6:
        return '全能';
      default:
        return '暂无';
    }
  }

  Widget matchItem(String name, String value) {
    return Container(
      width: 70.w,
      height: 60.w,
      padding: EdgeInsets.only(left: 10.w, top: 12.w, bottom: 12.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(8.r)),
        color: Colours.color0F0F16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            name,
            style: TextStyles.regular
                .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
          ),
          Text(value,
              style: TextStyles.din
                  .copyWith(fontSize: 16.sp, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget gameWidget(String name, String value) {
    return Container(
      width: (ScreenUtil().screenWidth - 60.w) / 3,
      height: 80.w,
      padding: EdgeInsets.only(left: 15.w, top: 20.w, bottom: 20.w),
      decoration: BoxDecoration(
          image: DecorationImage(
              image: WxAssets.images.mineCareerBg.provider(),
              fit: BoxFit.fill)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            name,
            style: TextStyles.regular
                .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
          ),
          Text(value,
              style: TextStyles.din
                  .copyWith(fontSize: 16.sp, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
