import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/careerAndHighlights/my_highlights_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:intl/intl.dart';

/// 赛程列表页面
class MyHighlightsPage extends StatelessWidget {
  MyHighlightsPage({super.key});
  final logic = Get.put(MyHighlightsLogic());
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: InkWell(
              onTap: () async{
                final result = await _showBattleTypeBottomSheet(context);
                log("!!!!!!!$result");
              },
              child: Container(
                  width: 60.w,
                  height: 44.w,
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.all(Radius.circular(33.r))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '场馆端集锦',
                        style: TextStyles.semiBold14,
                      ),
                      Icon(
                        Icons.expand_more,
                        color: Colours.white,
                        size: 25.w,
                      ),
                    ],
                  )),
            )),
            Container(
              width: 60.w,
              height: 44.w,
              alignment: Alignment.center,
              margin: EdgeInsets.only(left: 15.w),
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.all(Radius.circular(33.r))),
              child: Text(
                '共200',
                style:
                    TextStyles.display12.copyWith(color: Colours.color5C5C6E),
              ),
            ),
          ],
        ).marginSymmetric(horizontal: 15.w),
        Expanded(
            child: TabBarView(
          controller: logic.tabController,
          children: [
            ...logic.tabList.map((e) => NotificationListener(
                onNotification: (ScrollNotification note) {
                  if (note.metrics.pixels == note.metrics.maxScrollExtent) {
                    logic.loadMore();
                  }
                  return true;
                },
                child: Obx(() => RefreshIndicator(
                      onRefresh: logic.onRefresh,
                      child: Container(
                        color: Colours.bg_color,
                        child: logic.init.value
                            ? (logic.dataList.isEmpty
                                ? _buildEmptyView(context)
                                : _listView(context))
                            : buildLoad(),
                      ),
                    ))))
          ],
        ))
      ],
    );
  }

  /// 显示约战类型选择底部弹窗
  void _showBattleTypeBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部指示条
              Container(
                width: 38.w,
                height: 3.w,
                margin: EdgeInsets.only(top: 6.w, bottom: 10.w),
                decoration: BoxDecoration(
                  color: Colours.color1AD8D8D8,
                  borderRadius: BorderRadius.circular(2.5.r),
                ),
              ),

              // 全场约战选项
              _buildBattleOption(
                context,
                title: logic.highlightsTypeList[0]["title"] ?? '',
                onTap: () {
                  Navigator.pop(context, logic.highlightsTypeList[0]);
                },
              ),
              Container(
                height: 1,
                width: ScreenUtil().screenWidth - 30.w,
                color: Colours.color2F2F3B,
              ),
              // 半场约战选项
              _buildBattleOption(
                context,
                title: logic.highlightsTypeList[1]["title"] ?? '',
                onTap: () {
                   Navigator.pop(context, logic.highlightsTypeList[1]);
                },
              ),

              // 底部安全区域
              SizedBox(height: ScreenUtil().bottomBarHeight + 20.w),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBattleOption(
    BuildContext context, {
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        child: Center(
          child: Text(
            title,
            style: TextStyles.semiBold,
          ),
        ),
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: myNoDataView(
        context,
        msg: '暂无赛程',
        imagewidget: WxAssets.images.battleEmptyIcon.image(),
      ),
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.all(15.w),
        itemCount: logic.dataList.length,
        itemBuilder: (context, index) {
          var model = logic.dataList[index];
          DateTime parsedStartDate = DateTime.parse(model.startTime ?? '');
          DateTime parsedEndDate = DateTime.parse(model.endTime ?? '');
          String formattedStartDate =
              DateFormat("yyyy.MM.dd").format(parsedStartDate);
          String formattedEndDate =
              DateFormat("yyyy.MM.dd").format(parsedEndDate);
          return Column(
            children: [
              InkWell(
                onTap: () => AppPage.to(Routes.competitionDetailPage,
                    arguments: {'competitionId': model.competitionId}),
                child: Container(
                  height: 130.w,
                  margin: EdgeInsets.only(bottom: 15.w),
                  padding: EdgeInsets.all(15.r),
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.all(Radius.circular(8.r))),
                  child: Row(
                    children: [
                      ClipRRect(
                          borderRadius: BorderRadius.circular(8.w),
                          child: CachedNetworkImage(
                            imageUrl: model.arenaImageUrl ?? "",
                            width: 100.w,
                            height: 100.w,
                            fit: BoxFit.fill,
                          )),
                      SizedBox(
                        width: 15.w,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              model.competitionName ?? '',
                              style:
                                  TextStyles.semiBold14.copyWith(height: 1.5),
                              maxLines: 2,
                            ),
                            Text(
                              '报名截止：${model.registrationDeadline?.split(' ').first}',
                              style: TextStyles.display12,
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  height: 20,
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 9.w),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    gradient: model.status == 1
                                        ? const LinearGradient(colors: [
                                            Color(0xFF7732ED),
                                            Color(0xFFA555EF),
                                          ])
                                        : null,
                                    color: _getStatusColor(model.status ?? 0),
                                    borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(8.r),
                                        topRight: Radius.circular(8.r)),
                                  ),
                                  child: Text(
                                    _getStatusStr(model.status ?? 0),
                                    style: TextStyles.display10.copyWith(
                                        color: model.status == 4
                                            ? Colours.color5C5C6E
                                            : Colours.white,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                                const SizedBox(
                                  width: 6,
                                ),
                                Text(
                                  '$formattedStartDate-$formattedEndDate',
                                  style: TextStyles.display12
                                      .copyWith(color: Colours.colorA8A8BC),
                                )
                              ],
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              )
            ],
          );
        });
  }

  _getStatusStr(int status) {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '报名中';
      case 2:
        return '待开赛';
      case 3:
        return '进行中';
      case 4:
        return '已结束';
      default:
        return '未开始';
    }
  }

  _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colours.color6435E9;
      case 1:
        return Colours.color6435E9;
      case 2:
        return Colours.color6435E9;
      case 3:
        return Colours.colorFF661A;
      case 4:
        return Colours.color262626;
      default:
        return Colours.color262626;
    }
  }
}
