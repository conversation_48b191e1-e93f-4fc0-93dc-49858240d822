import 'dart:async';
import 'dart:io';
import 'package:carousel_slider/carousel_controller.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/widgets/permission_dialog.dart';
import 'tab_venue_state.dart';

class TabVenueLogic extends GetxController {
  final TabVenueState state = TabVenueState();
  final CarouselSliderController carouselController =
      CarouselSliderController();
  @override
  void onInit() async {
    super.onInit();
    if (Platform.isAndroid) {
      if (await LocationUtils.instance.checkPermission()) {
        onRefreshList();
      } else {
        state.init.value = true;
      }
    } else {
      if (await LocationUtils.instance.requestPermission(Get.context!)) {
        onRefreshList();
      } else {
        state.init.value = true;
      }
    }

    state.subscription = BusUtils.instance.on((action) {
      if (EventBusKey.getLocation == action.key) {
        onRefreshList();
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    state.subscription?.cancel();
  }

  Future<void> onRefresh() async {
    await onRefreshList();
  }

  Future<void> onRefreshList() async {
    if (await LocationUtils.instance.checkPermission()) {
      await LocationUtils.instance.getCurrentPosition();
    }

    final position = LocationUtils.instance.position;
    if (position == null) {
      state.init.value = true;
      return;
    }
    final res = await Api().get(ApiUrl.recommendedStadium, queryParameters: {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
      'page': 1,
      'limit': 5
    });
    if (state.init.value) {
      carouselController.jumpToPage(0);
    }
    state.init.value = true;
    if (res.isSuccessful()) {
      var sum = (res.data['results'] as List<dynamic>)
          .map((e) => PlaceModel.fromJson(e))
          .toList()
          .length;
      state.list.value = (res.data['results'] as List<dynamic>)
          .map((e) => PlaceModel.fromJson(e))
          .toList()
          .sublist(0, sum >= 5 ? 5 : sum);
    }
  }

  void toAll() {
    if (!LocationUtils.instance.havePermission.value) {
      showLocationDialog();
      return;
    }
    AppPage.to(Routes.place);
  }

  void openSettings() async {
    final result = await WxStorage.instance.getBool('requestPermission');
    if (Platform.isAndroid && result == null) {
      WxStorage.instance.setBool('requestPermission', true);
      if (await LocationUtils.instance.requestPermission(Get.context!)) {
        onRefreshList();
      }
    } else {
      LocationUtils.instance.openSettings();
    }
  }
}
