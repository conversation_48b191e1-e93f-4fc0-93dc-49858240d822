import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/widget/competition_calendar_logic.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/widgets/match_item_widget.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

/// 比赛日历页面
class CompetitionCalendarPage extends StatelessWidget {
  CompetitionCalendarPage({super.key});

  final logic = Get.put(CompetitionCalendarLogic());
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colours.bg_color,
      child: Column(
        children: [
          _dateSel(),
          Expanded(
              child: RefreshIndicator(
            onRefresh: logic.onRefresh,
            child: Container(
              color: Colours.bg_color,
              child: Obx(() => logic.init.value
                  ? (logic.dataList.isEmpty
                      ? _buildEmptyView(context)
                      : _listView(context))
                  : buildLoad()),
            ),
          ))
        ],
      ),
    );
  }

  Widget _dateSel() {
    return Container(
      height: 56.w,
      margin: EdgeInsets.only(top: 15.w),
      width: ScreenUtil().screenWidth - 30.w,
      // padding: EdgeInsets.symmetric(horizontal: 4.w),
      // color: Colors.red,
      child: Obx(
        () => ScrollablePositionedList.builder(
          scrollDirection: Axis.horizontal,
          itemCount: logic.dateList.length,
          itemScrollController: logic.scrollController,
          initialScrollIndex: 12,
          itemBuilder: (context, index) {
            Map<String, dynamic> dateDic = logic.dateList[index];
            return GestureDetector(
              onTap: () {
                logic.selectedDate(dateDic['date']);
              },
              child: Container(
                width: 50.w,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: dateDic["isCenterDate"]
                      ? const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        )
                      : null,
                  borderRadius: BorderRadius.circular(12.w),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      dateDic['weekday'] ?? "",
                      style: TextStyle(
                        color: dateDic["isCenterDate"]
                            ? Colors.white
                            : Colours.colorA8A8BC,
                        fontWeight: AppFontWeight.bold(),
                        fontSize: 12.sp,
                      ),
                    ),
                    SizedBox(
                      height: 6.w,
                    ),
                    Text(
                      dateDic['dateFormatStr'] ?? "",
                      style: TextStyle(
                        fontFamily: 'DIN',
                        color: dateDic["isCenterDate"]
                            ? Colors.white
                            : Colours.colorA8A8BC,
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: myNoDataView(context,
          msg: '暂无赛程', imagewidget: WxAssets.images.battleEmptyIcon.image()),
    );
  }

  Widget _listView(BuildContext context) {
    return ListView.builder(
        padding: const EdgeInsets.all(15),
        itemCount: logic.dataList.length,
        itemBuilder: (context, index) {
          final model = logic.dataList[index];
          return Column(
            children: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => AppPage.to(Routes.arenaDetailsPage,
                    arguments: {"id": int.parse(model.arenaId)}),
                child: SizedBox(
                  height: 34.w,
                  child: Row(
                    children: [
                      Text(
                        model.arenaName,
                        style: TextStyles.display12
                            .copyWith(color: Colours.colorA8A8BC),
                      ),
                      const Spacer(),
                      WxAssets.images.icArrowRight
                          .image(width: 12.w, height: 12.w),
                    ],
                  ),
                ),
              ),
              Column(
                children: model.matches
                    .map((e) => MatchItemWidget.fromMatches(
                          matchModel: e,
                          showStatus: true,
                        ))
                    .toList(),
              ),
            ],
          );
        });
  }
}
