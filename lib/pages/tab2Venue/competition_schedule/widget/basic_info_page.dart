import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/src/extensions/widget_extensions.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/invite_mini_path_model.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/competition_detail_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/pay/fluwx_utils.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:ui_packages/ui_packages.dart';

class BasicInfoPage extends StatefulWidget {
  final CompetitionDetailModel competitionModel;
  const BasicInfoPage({super.key, required this.competitionModel});

  @override
  State<BasicInfoPage> createState() => _BasicInfoPageState();
}

class _BasicInfoPageState extends State<BasicInfoPage> {
  int _currentIndex = 0;
  final CarouselSliderController _carouselController =
      CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildContent(),
      bottomNavigationBar: widget.competitionModel.status == 1
          ? Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: 15.w),
              margin: EdgeInsets.only(
                  left: 15.w,
                  right: 15.w,
                  bottom: ScreenUtil().bottomBarHeight),
              child: Row(
                children: [
                  InkWell(
                    onTap: () async {
                      //分享到微信
                      InviteMiniPathModel inviteMiniPathModel =
                          InviteMiniPathModel(
                        appRawId: FluwxUtils.miniProgramUserName,
                        appId: FluwxUtils.appId,
                        path:
                            "pagesMatch/schedule/scheduleInfo.html?id=${widget.competitionModel.competitionId}",
                        image: "",
                        icon: "",
                        title: widget.competitionModel.competitionName ?? "",
                      );
                      ByteData data = await rootBundle.load(
                          "assets/images/dialog_invitation.png"); //dialog_invitation
                      Uint8List imageBytes = data.buffer.asUint8List();

                      MyShareH5.shareMiniProgram(
                          inviteMiniPathModel, imageBytes);
                    },
                    child: WxAssets.images.shareSendIcon.image(),
                  ),
                  SizedBox(
                    width: 15.w,
                  ),
                  Expanded(
                      child: InkWell(
                    onTap: () {
                      AppPage.to(Routes.competitionSignUpPage, arguments: {
                        'competitionId': widget.competitionModel.competitionId
                      });
                    },
                    child: Container(
                      height: 44.w,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Colours.color7732ED, Colours.colorA555EF],
                            begin: Alignment.bottomLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(25.r)),
                      child: Text(
                        '去报名',
                        style: TextStyles.semiBold14,
                      ),
                    ),
                  ))
                ],
              ),
            )
          : null,
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildImageCarousel(),
          Row(
            children: [
              Container(
                height: 20,
                padding: EdgeInsets.symmetric(horizontal: 9.w),
                alignment: Alignment.centerLeft,
                decoration: BoxDecoration(
                  gradient: widget.competitionModel.status == 1
                      ? const LinearGradient(colors: [
                          Color(0xFF7732ED),
                          Color(0xFFA555EF),
                        ])
                      : null,
                  color: _getStatusColor(widget.competitionModel.status ?? 0),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8.r),
                      topRight: Radius.circular(8.r)),
                ),
                child: Text(
                  _getStatusStr(widget.competitionModel.status ?? 0),
                  style: TextStyles.display10.copyWith(
                      color: widget.competitionModel.status == 4
                          ? Colours.color5C5C6E
                          : Colours.white,
                      fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(
                width: 6,
              ),
              SizedBox(
                width: ScreenUtil().screenWidth - 95,
                child: Text(
                  widget.competitionModel.competitionName ?? '',
                  style: TextStyles.titleSemiBold16,
                  maxLines: 1,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextWithIcon(title: S.current.the_participating_teams),
              (widget.competitionModel.teamInfo ?? []).isNotEmpty
                  ? GestureDetector(
                      onTap: () {
                        AppPage.to(Routes.competitionTeamsPage, arguments: {
                          'competitionId': widget.competitionModel.competitionId
                        });
                      },
                      child: Row(
                        children: [
                          Text(
                            S.current.view_more,
                            style: TextStyles.display12
                                .copyWith(color: const Color(0xFFA8A8BC)),
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          const Icon(
                            Icons.arrow_forward_ios,
                            size: 14,
                            color: Color(0xFFA8A8BC),
                          )
                        ],
                      ),
                    )
                  : const SizedBox()
            ],
          ).marginOnly(top: 20.w, bottom: 15.w),
          Container(
              height: 128.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: Colours.color191921,
              ),
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.all(15.w),
              child: (widget.competitionModel.teamInfo ?? []).isEmpty
                  ? Center(
                      child: Text(
                        '暂无球队',
                        style: TextStyles.textSize14
                            .copyWith(color: Colours.color5C5C6E),
                      ),
                    )
                  : Wrap(
                      spacing: (ScreenUtil().screenWidth - 60.w - 4 * 70.w) / 3,
                      runSpacing: 12.w,
                      children: widget.competitionModel.teamInfo!
                          .map((e) => InkWell(
                                onTap: () => AppPage.to(Routes.teamInfoPage,
                                    arguments: {
                                      'teamId': (e?.teamId ?? 0).toString()
                                    }),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8.r),
                                      child: CachedNetworkImage(
                                        imageUrl: e?.teamLogo ?? '',
                                        width: 70.w,
                                        height: 70.w,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    SizedBox(
                                      height: 12.w,
                                    ),
                                    SizedBox(
                                      width: 65.w,
                                      child: Text(
                                        textAlign: TextAlign.center,
                                        e?.teamName ?? '',
                                        maxLines: 1,
                                        style: TextStyles.regular.copyWith(
                                            fontSize: 12.sp,
                                            color: Colours.white),
                                      ),
                                    )
                                  ],
                                ),
                              ))
                          .toList(),
                    )),
          Container(
            margin: EdgeInsets.only(top: 20.w),
            alignment: Alignment.centerLeft,
            child: const TextWithIcon(title: '参与球馆'),
          ),
          (widget.competitionModel.arenaInfoList ?? []).isEmpty
              ? Container(
                  height: 128.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    color: Colours.color191921,
                  ),
                  alignment: Alignment.center,
                  child: Center(
                    child: Text(
                      '暂无球馆',
                      style: TextStyles.textSize14
                          .copyWith(color: Colours.color5C5C6E),
                    ),
                  ),
                )
              : Column(
                  children: widget.competitionModel.arenaInfoList!
                      .map((e) => _buildArenaCell(e!))
                      .toList(),
                ),
          Container(
            margin: EdgeInsets.only(top: 20.w, bottom: 15.w),
            alignment: Alignment.centerLeft,
            child: const TextWithIcon(title: '赛事信息'),
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              color: Colours.color191921,
            ),
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.w),
            child: Column(
              children: [
                _buildInfoItem(
                    '报名时间', widget.competitionModel.registrationDeadline),
                SizedBox(
                  height: 20.w,
                ),
                _buildInfoItem('比赛时间', widget.competitionModel.startTime),
                SizedBox(
                  height: 20.w,
                ),
                _buildInfoItem('联系电话', widget.competitionModel.contactPhone,
                    showCopyIcon: true),
                SizedBox(
                  height: 20.w,
                ),
                _buildInfoItem(
                    '报名费用', '¥${widget.competitionModel.registrationFee}'),
                SizedBox(
                  height: 20.w,
                ),
                _buildInfoItem('奖品', widget.competitionModel.prizeName),
                SizedBox(
                  height: 20.w,
                ),
                _buildInfoItem('奖品图片', widget.competitionModel.prizeName,
                    prizeImages: widget.competitionModel.PrizeImages ?? []),
              ],
            ),
          )
        ],
      ).marginSymmetric(horizontal: 15.w),
    );
  }

  Widget _buildImageCarousel() {
    final images = widget.competitionModel.competitionImages;

    // 如果没有图片，显示占位符
    if (images == null || images.isEmpty) {
      return Container(
        margin: EdgeInsets.symmetric(vertical: 15.w),
        width: double.infinity,
        height: 194.w,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(
          child: Icon(
            Icons.image_not_supported,
            size: 50.w,
            color: Colors.grey,
          ),
        ),
      );
    }

    return Container(
      margin: EdgeInsets.symmetric(vertical: 15.w),
      child: Stack(
        children: [
          // 轮播图
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: CarouselSlider(
              carouselController: _carouselController,
              options: CarouselOptions(
                height: 194.w,
                viewportFraction: 1.0,
                autoPlay: images.length > 1,
                autoPlayInterval: const Duration(seconds: 3),
                enlargeCenterPage: false,
                onPageChanged: (index, reason) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
              ),
              items: images.map((imageUrl) {
                return Builder(
                  builder: (BuildContext context) {
                    return SizedBox(
                      width: double.infinity,
                      child: CachedNetworkImage(
                        imageUrl: imageUrl ?? '',
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[200],
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[200],
                          child: const Center(
                            child: Icon(
                              Icons.error,
                              color: Colors.grey,
                              size: 50,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              }).toList(),
            ),
          ),
          // 指示器 (只有多张图片时显示) - 定位在轮播图底部10pt处
          if (images.length > 1)
            Positioned(
              bottom: 10.w,
              left: 0,
              right: 0,
              child: Center(
                child: AnimatedSmoothIndicator(
                  activeIndex: _currentIndex,
                  count: images.length,
                  effect: ExpandingDotsEffect(
                    dotColor: Colors.grey[400]!,
                    activeDotColor: Colors.white,
                    dotHeight: 6.w,
                    dotWidth: 6.w,
                    expansionFactor: 2,
                    spacing: 4.w,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  _getStatusStr(int status) {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '报名中';
      case 2:
        return '待开赛';
      case 3:
        return '进行中';
      case 4:
        return '已结束';
      default:
        return '未开始';
    }
  }

  _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colours.color6435E9;
      case 1:
        return Colours.color6435E9;
      case 2:
        return Colours.color6435E9;
      case 3:
        return Colours.colorFF661A;
      case 4:
        return Colours.color262626;
      default:
        return Colours.color6435E9;
    }
  }

  _buildInfoItem(String title, String? value,
      {bool showCopyIcon = false, List prizeImages = const []}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyles.regular
              .copyWith(color: Colours.color5C5C6E, fontSize: 14),
        ),
        SizedBox(
          width: 15.w,
        ),
        Text(
          value ?? '',
          style: TextStyles.regular
              .copyWith(color: Colours.color9393A5, fontSize: 14),
        ),
        if (showCopyIcon)
          GestureDetector(
            onTap: () {
              Clipboard.setData(ClipboardData(text: value ?? '')).then((_) {
                WxLoading.showToast('已复制');
              });
            },
            child: WxAssets.images.imgCopy.image(width: 14.w, height: 14.w),
          ).marginSymmetric(horizontal: 10.w),
        if (prizeImages.isNotEmpty)
          Row(
            children: prizeImages
                .map((e) => ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: CachedNetworkImage(
                      imageUrl: e ?? '',
                      width: 60.w,
                      height: 60.w,
                      fit: BoxFit.cover,
                    )))
                .toList(),
          )
      ],
    );
  }

  Widget _buildArenaCell(CompetitionDetailModelArenaInfoList model) {
    return InkWell(
      onTap: () =>
          AppPage.to(Routes.arenaDetailsPage, arguments: {"id": model.arenaId}),
      child: Container(
          height: 116.w,
          padding: EdgeInsets.all(15.w),
          margin: EdgeInsets.only(top: 15.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            color: Colours.color22222D,
          ),
          alignment: Alignment.center,
          child: Row(
            children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: CachedNetworkImage(
                    imageUrl: model.arenaLogo ?? '',
                    width: 86.w,
                    height: 86.w,
                    fit: BoxFit.cover,
                  )),
              SizedBox(
                width: 10.w,
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      maxLines: 1,
                      model.arenaName ?? '',
                      style: TextStyles.semiBold14,
                    ),
                    (model.rounds ?? []).isNotEmpty
                        ? Wrap(
                            spacing: 6.w,
                            runSpacing: 6.w,
                            children: model.rounds!
                                .map((e) => Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 8.w, vertical: 5.w),
                                      decoration: BoxDecoration(
                                        color: Colours.color2E1575,
                                        border: Border.all(
                                          width: 1,
                                          color: Colours.color6435E9,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(10.r),
                                      ),
                                      child: Text(
                                        textAlign: TextAlign.center,
                                        '$e ',
                                        style: TextStyles.medium.copyWith(
                                            fontSize: 10.sp,
                                            color: Colours.white),
                                      ),
                                    ))
                                .toList(),
                          )
                        : Container(
                            color: Colors.red,
                          )
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios,
                  size: 14.w, color: Colours.colorA8A8BC),
            ],
          )),
    );
  }
}
