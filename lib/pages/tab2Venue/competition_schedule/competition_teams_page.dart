import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/competition_teams_logic.dart';
import 'package:shoot_z/pages/tab2Venue/competition_schedule/model/participating_teams_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 二级页面->我的球队列表
class CompetitionTeamsPage extends StatelessWidget {
  CompetitionTeamsPage({super.key});

  final logic = Get.put(CompetitionTeamsLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.the_participating_teams),
      ),
      body: _listWidget(context),
    );
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return logic.dataTeamList.isEmpty
          ? myNoDataView(
              context,
              msg: S.current.No_data_available,
              imagewidget: WxAssets.images.battleEmptyIcon.image(),
            )
          : ListView.builder(
              scrollDirection: Axis.vertical,
              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
              shrinkWrap: true,
              padding: EdgeInsets.only(bottom: 40.w),
              physics: const NeverScrollableScrollPhysics(),
              itemCount: logic.dataTeamList.length,
              itemBuilder: (context, position) {
                return _listItemWidget(logic.dataTeamList[position]);
              });
    });
  }

  /// 构建列表项
  Widget _listItemWidget(ParticipatingTeamsModel item) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // if (logic.isSelectTeam) {
        //   AppPage.back(result: item);
        //   return;
        // }
        AppPage.to(Routes.teamInfoPage, arguments: {
          'teamId': item.teamId.toString(),
        });
      },
      child: Container(
        margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
        padding: EdgeInsets.all(15.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: Colours.color191921),
        child: Row(
          children: [
            Stack(
              children: [
                MyImage(
                  item.teamLogo ?? "",
                  width: 46.w,
                  height: 46.w,
                  radius: 23.r,
                  placeholderImage: "my_team_head4.png",
                  errorImage: "my_team_head4.png",
                ),
              ],
            ),
            SizedBox(
              width: 15.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      RichText(
                        text: TextSpan(
                          //style: DefaultTextStyle.of(context).style,
                          children: <InlineSpan>[
                            TextSpan(
                              text: "${item.teamName ?? ""} ",
                              style: TextStyles.regular.copyWith(
                                  fontWeight: FontWeight.w600, fontSize: 16.sp),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        width: 5.w,
                      ),
                      Image.asset(
                        item.imagePath ?? '',
                        width: 24.w,
                        height: 24.w,
                      )
                    ],
                  ),
                  SizedBox(
                    height: 8.w,
                  ),
                  Text('ID：${item.teamId ?? ""}',
                      style: TextStyles.regular.copyWith(
                          fontSize: 12.sp, color: Colours.color5C5C6E)),
                ],
              ),
            ),
            MyImage("ic_arrow_right.png",
                width: 14.w,
                height: 14.w,
                isAssetImage: true,
                imageColor: Colours.color9393A5),
          ],
        ),
      ),
    );
  }
}
