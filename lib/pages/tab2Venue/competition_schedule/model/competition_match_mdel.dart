///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class CompetitionMatchModel {
/*
{
  "arenaId": 0,
  "arenaName": "string",
  "leftRankScore": 0,
  "leftScore": 0,
  "leftTeamId": 0,
  "leftTeamLogo": "string",
  "leftTeamName": "string",
  "markStatus": 0,
  "matchCourt": "string",
  "matchDataWeek": "string",
  "matchId": 0,
  "rightRankScore": 0,
  "rightScore": 0,
  "rightTeamId": 0,
  "rightTeamLogo": "string",
  "rightTeamName": "string",
  "roundName": "string",
  "startTime": "string",
  "status": 0
} 
*/

  int? arenaId;
  String? arenaName;
  int? leftRankScore;
  int? leftScore;
  int? leftTeamId;
  String? leftTeamLogo;
  String? leftTeamName;
  int? markStatus;
  String? matchCourt;
  String? matchDataWeek;
  int? matchId;
  int? rightRankScore;
  int? rightScore;
  int? rightTeamId;
  String? rightTeamLogo;
  String? rightTeamName;
  String? roundName;
  String? startTime;
  int? status;

  CompetitionMatchModel({
    this.arenaId,
    this.arenaName,
    this.leftRankScore,
    this.leftScore,
    this.leftTeamId,
    this.leftTeamLogo,
    this.leftTeamName,
    this.markStatus,
    this.matchCourt,
    this.matchDataWeek,
    this.matchId,
    this.rightRankScore,
    this.rightScore,
    this.rightTeamId,
    this.rightTeamLogo,
    this.rightTeamName,
    this.roundName,
    this.startTime,
    this.status,
  });
  CompetitionMatchModel.fromJson(Map<String, dynamic> json) {
    arenaId = json['arenaId']?.toInt();
    arenaName = json['arenaName']?.toString();
    leftRankScore = json['leftRankScore']?.toInt();
    leftScore = json['leftScore']?.toInt();
    leftTeamId = json['leftTeamId']?.toInt();
    leftTeamLogo = json['leftTeamLogo']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    markStatus = json['markStatus']?.toInt();
    matchCourt = json['matchCourt']?.toString();
    matchDataWeek = json['matchDataWeek']?.toString();
    matchId = json['matchId']?.toInt();
    rightRankScore = json['rightRankScore']?.toInt();
    rightScore = json['rightScore']?.toInt();
    rightTeamId = json['rightTeamId']?.toInt();
    rightTeamLogo = json['rightTeamLogo']?.toString();
    rightTeamName = json['rightTeamName']?.toString();
    roundName = json['roundName']?.toString();
    startTime = json['startTime']?.toString();
    status = json['status']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenaId'] = arenaId;
    data['arenaName'] = arenaName;
    data['leftRankScore'] = leftRankScore;
    data['leftScore'] = leftScore;
    data['leftTeamId'] = leftTeamId;
    data['leftTeamLogo'] = leftTeamLogo;
    data['leftTeamName'] = leftTeamName;
    data['markStatus'] = markStatus;
    data['matchCourt'] = matchCourt;
    data['matchDataWeek'] = matchDataWeek;
    data['matchId'] = matchId;
    data['rightRankScore'] = rightRankScore;
    data['rightScore'] = rightScore;
    data['rightTeamId'] = rightTeamId;
    data['rightTeamLogo'] = rightTeamLogo;
    data['rightTeamName'] = rightTeamName;
    data['roundName'] = roundName;
    data['startTime'] = startTime;
    data['status'] = status;
    return data;
  }
}
