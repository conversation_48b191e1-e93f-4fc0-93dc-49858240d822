import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab4PointsMall/points_goods_type/points_goods_type_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的->积分页面
class PointsGoodsTypePage extends StatefulWidget {
  const PointsGoodsTypePage({super.key});

  @override
  State<PointsGoodsTypePage> createState() => _RankingsPageState();
}

class _RankingsPageState extends State<PointsGoodsTypePage> {
  final logic = Get.put(PointsGoodsTypeLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: MyAppBar(
          title: Text(S.current.points_shopping_Mall),
        ),
        body: Obx(() {
          return Center(
            child: (logic.isFrist.value)
                ? buildLoad()
                : Row(
                    children: [
                      Container(
                        width: 100.w,
                        color: Colours.color191921,
                        child: ListView.builder(
                          itemCount: logic.tabs.length,
                          itemBuilder: (context, index) {
                            return Obx(() {
                              return GestureDetector(
                                onTap: () {
                                  logic.changeIndex(index);
                                },
                                child: Container(
                                  width: 100.w,
                                  height: 44.w,
                                  alignment: Alignment.center,
                                  color: logic.tabbarIndex.value == index
                                      ? Colours.color0F0F16
                                      : Colors.transparent,
                                  child: Text(
                                    logic.tabs[index].title,
                                    style: TextStyle(
                                      color: logic.tabbarIndex.value == index
                                          ? Colours.white
                                          : Colours.color5C5C6E,
                                      fontSize: 14.sp,
                                    ),
                                  ),
                                ),
                              );
                            });
                          },
                        ),
                      ),
                      Expanded(
                          child: logic.isFristGoods.value
                              ? buildLoad()
                              : TabBarView(
                                  controller: logic.tabController,
                                  physics:
                                      const NeverScrollableScrollPhysics(), // 禁止所有滚动,
                                  children: logic.tabs
                                      .map((tab) => tab.page)
                                      .toList(),
                                )),
                    ],
                  ),
          );
        }));
  }
}
