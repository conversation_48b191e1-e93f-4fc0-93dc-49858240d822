import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'dart:async';
import 'dart:developer';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/goods_detail_model.dart';
import 'package:shoot_z/network/model/goods_list_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///一级页面->积分商城
class TabPonitMallItemPage2 extends StatefulWidget {
  final String tabId;
  const TabPonitMallItemPage2({super.key, required this.tabId});

  @override
  State<TabPonitMallItemPage2> createState() => _SearchPageState();
}

class _SearchPageState extends State<TabPonitMallItemPage2> {
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  StreamSubscription? subscription;
  var dataFag = {
    "page": 1,
    "isFrist": true,
    "pageSize": 20,
  }.obs;
  var goodsDetailModel = GoodsDetailModel().obs; //选中的商品详情
  final phoneController = TextEditingController();
  final addressController = TextEditingController();
  final nameController = TextEditingController();
  var colorNameList = <String>[].obs;
  var sizeNameList = <String>[].obs;
  var indexColorName = 0.obs;
  var indexSizeName = 0.obs;
  var indexGoodsList = 0.obs;
  //数据列表
  var dataList = <GoodsListModel>[].obs;

  @override
  void initState() {
    super.initState();
    getdataList(isLoad: false);
  }

  @override
  void dispose() {
    super.dispose();
    subscription?.cancel();
  }

  //获得最新列表
  getdataList({isLoad = true}) async {
    if (isLoad) {
      dataFag["page"] = (dataFag["page"] as int) + 1;
    } else {
      dataFag["page"] = 1;
    }

    Map<String, dynamic> param = {
      'page': dataFag["page"] ?? 1,
      'limit': 10,
      'categoryId': widget.tabId, //分类 1.限时兑换 2.篮球周边 3.会员兑换
      //goodsName
    };
    log("pointsGoodsList1-${widget.tabId}-$param");
    var res = await Api().get(ApiUrl.pointsGoodsList, queryParameters: param);
    if (res.isSuccessful()) {
      List list = res.data["list"] ?? [];
      List<GoodsListModel> modelList =
          list.map((e) => GoodsListModel.fromJson(e)).toList();
      log("pointsGoodsList1-${res.data}");
      if (isLoad) {
        dataList.addAll(modelList);
        dataList.refresh();
        if (modelList.length < 20) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      } else {
        refreshController.resetNoData();
        dataList.assignAll(modelList);
        refreshController.refreshCompleted();
      }
    } else {
      refreshController.refreshCompleted();
      WxLoading.showToast(res.message);
    }
    if (dataFag["isFrist"] as bool) {
      dataFag["isFrist"] = false;
      dataFag.refresh();
    }
  }

  //兑换商品弹窗
  void getExchangeGoodsDialog(String title, String sureText,
      GoodsListModel goodsListModel, void Function()? onPressed) {
    Get.dialog(BaseDialog(
      title: title,
      onPressed: onPressed,
      isShowCannel: true,
      sureText: sureText,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(30),
              child: MyImage(
                goodsListModel.imageUrl ?? '',
                //  holderImg: "home/index/df_banner_top",
                fit: BoxFit.fill,
                width: 174.w,
                height: 116.w,
                bgColor: Colours.white,
                errorImage: "error_img_white.png",
                placeholderImage: "error_img_white.png",
                radius: 5.r,
              ),
            ),
            SizedBox(
              height: 5.w,
            ),
            RichText(
              text: TextSpan(
                  text: S.current.Confirmed_use,
                  style: TextStyle(
                      color: Colours.color5C5C6E,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold),
                  children: <InlineSpan>[
                    TextSpan(
                        text: "${goodsListModel.pointPrice ?? "-"}",
                        style: TextStyle(
                            color: Colours.colorA44EFF,
                            fontSize: 26.sp,
                            fontWeight: FontWeight.normal)),
                    TextSpan(
                        text: S.current.Point_exchange,
                        style: TextStyle(
                            color: Colours.color5C5C6E,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.normal)),
                  ]),
            ),
          ],
        ),
      ),
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Obx(() {
        return SmartRefresher(
          controller: refreshController,
          footer: buildFooter(),
          header: buildClassicHeader(),
          enablePullDown: false,
          enablePullUp: dataList.isNotEmpty,
          onLoading: () {
            getdataList();
          },
          physics: const AlwaysScrollableScrollPhysics(),
          //  physics: const NeverScrollableScrollPhysics(),
          child: (dataFag["isFrist"] as bool)
              ? buildLoad()
              : dataList.isEmpty
                  ? SizedBox(
                      height: 480.w,
                      child: myNoDataView(
                        context,
                        msg: S.current.No_data_available,
                        imagewidget: WxAssets.images.icGameNo
                            .image(width: 150.w, height: 150.w),
                      ))
                  : Padding(
                      padding: EdgeInsets.only(left: 15.w, right: 15.w),
                      child: GridView.builder(
                          scrollDirection: Axis.vertical,
                          // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 15,
                            mainAxisSpacing: 15,
                            childAspectRatio: 115 / 180,
                          ),
                          itemCount: dataList.length,
                          itemBuilder: (context, position) {
                            // String base64 =  ImageUtils.netImageToBase64(images[position]);
                            // myLog(base64, StackTrace.current);
                            return GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                AppPage.to(Routes.goodsInfoPage,
                                    arguments: {
                                      "goodsId": "${dataList[position].spuId}",
                                    },
                                    needLogin: true);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colours.color0F0F16,
                                  borderRadius: BorderRadius.circular(10.r),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    MyImage(
                                      dataList[position].imageUrl ?? '',
                                      fit: BoxFit.fitWidth,
                                      width: double.infinity,
                                      height: 115.w,
                                      radius: 10.r,
                                      bgColor: Colours.white,
                                      errorImage: "error_img_white.png",
                                      placeholderImage: "error_img_white.png",
                                    ),
                                    SizedBox(
                                      height: 12.w,
                                    ),
                                    Expanded(
                                      child: Text(
                                        dataList[position].goodsName ?? '',
                                        maxLines: 1,
                                        style: TextStyle(
                                            color: Colours.color9393A5,
                                            fontSize: 14.sp),
                                      ),
                                    ),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        WxAssets.images.points2
                                            .image(width: 16.w, height: 16.w),
                                        SizedBox(
                                          width: 3.w,
                                        ),
                                        Expanded(
                                          child: Text(
                                            "${dataList[position].pointPrice ?? ''}",
                                            style: TextStyle(
                                                color: Colours.white,
                                                fontSize: 16.sp),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 1,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                    ),
        );
      }),
    );
  }

  Container buildRowWidget(String name, String value) {
    return Container(
      height: 35.w,
      width: double.infinity,
      alignment: Alignment.centerLeft,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            maxLines: 1,
            style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
          ),
          SizedBox(
            width: 15.w,
          ),
          Expanded(
            child: Text(
              // 使用 Flexible 替代 Expanded
              value,
              maxLines: 1,
              style:
                  TextStyles.regular.copyWith(overflow: TextOverflow.ellipsis),
            ),
          ),
        ],
      ),
    );
  }
}
