// ignore_for_file: unnecessary_null_comparison

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/DynamicTab.dart';
import 'package:shoot_z/network/model/goods_category_list_model.dart';
import 'package:shoot_z/network/model/my_points_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab4PointsMall/tab_ponits_mall_item_view1.dart';
import 'package:shoot_z/utils/event_bus.dart';

class PointsMallLogic extends GetxController
    with WidgetsBindingObserver, GetTickerProviderStateMixin, RouteAware {
  final ScrollController scrollController = ScrollController();
  TabController? tabController;
  var tabbarIndex = 0.obs;
  var myPointsModel = MyPointsModel().obs;
  var isFrist = true.obs;
  var isFristGoods = true.obs;
  //数据列表
  var goodsCategoryList = <GoodsCategoryListModel>[].obs; //商品类别
  var tabs = <DynamicTab>[].obs;
  var type = 0.obs; //0 没有返回键 1有返回键
  StreamSubscription<EventAction>? subscription;
  @override
  void onInit() {
    super.onInit();
// 初始化空控制器
    tabController = TabController(
      vsync: this,
      length: 0,
      initialIndex: 0,
    );
    if (Get.arguments != null && Get.arguments is Map<String, dynamic>) {
      final args = Get.arguments as Map<String, dynamic>;
      type.value = args['type'] as int? ?? 0;
    }
    subscription = BusUtils.instance.on((p0) {
      if (p0.key == EventBusKey.changePoints) {
        if (UserManager.instance.isLogin) {
          if (UserManager.instance.isLogin) UserManager.instance.pullUserInfo();
        }
      }
    });
    WidgetsBinding.instance.addObserver(this);
    getPointsInfo();
    getGoodsCategoryList();
  }

  void switchTab(index) {
    tabbarIndex.value = index;
  }

  @override
  void didPopNext() {
    super.didPopNext();
    if (UserManager.instance.isLogin) UserManager.instance.pullUserInfo();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    // if (state == AppLifecycleState.resumed) {
    //   getPointsInfo();
    // } else if (state == AppLifecycleState.paused) {}
  }

  //获得积分数据
  Future<void> getPointsInfo() async {
    log("MineLogicgetPointsSignIn0");
    // WxLoading.show();
    Map<String, dynamic>? param = {};
    final res = await Api().get(ApiUrl.pointsMyPoint, queryParameters: param);
    log(jsonEncode(res.data));
    //WxLoading.dismiss();
    if (res.isSuccessful()) {
      myPointsModel.value = MyPointsModel.fromJson(res.data);
      myPointsModel.refresh();
      if (isFrist.value) {
        isFrist.value = false;
        isFrist.refresh();
      }
    } else {
      WxLoading.showToast(res.message);
    }
  }

  //获得商品类别
  getGoodsCategoryList() async {
    Map<String, dynamic> param = {
      'page': 1,
      'limit': 3,
    };
    var res =
        await Api().get(ApiUrl.pointsGoodsCategoryList, queryParameters: param);

    if (res.isSuccessful()) {
      log("pointsGoodsCategoryList-${jsonEncode(res.data)}");
      List list = res.data["list"];
      List<GoodsCategoryListModel> modelList =
          list.map((e) => GoodsCategoryListModel.fromJson(e)).toList();
      goodsCategoryList.addAll(modelList);
      for (var item in goodsCategoryList) {
        // 添加到列表
        tabs.add(DynamicTab(
          id: "${item.categoryId ?? ""}",
          title: item.name ?? "未命名",
          page: TabPonitMallItemPage1(
            tabId: "${item.categoryId ?? ""}",
            key: Key("${item.categoryId ?? item.name ?? ""}"),
          ),
          //data: data,
        ));
      }
      // tabs.refresh();
      // 创建新控制器
      tabController = TabController(
        vsync: this,
        length: tabs.length,
        initialIndex: 0,
      );
      // 添加索引监听
      tabController!.addListener(() {
        tabbarIndex.value = tabController!.index;
      });
      if (isFristGoods.value) {
        isFristGoods.value = false;
        // isFristGoods.refresh();
      }
      refresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }

  // 获取当前标签的控制器
  TabController? getCurrentTabController() {
    if (tabs.isEmpty) return null;
    final id = tabs[tabController!.index].id;
    return Get.find<TabController>(tag: id);
  }

  @override
  void onClose() {
    subscription?.cancel();
    // 销毁TabController
    if (isTabControllerInitialized) {
      tabController?.dispose();
    }
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  bool get isTabControllerInitialized {
    return tabController != null &&
        tabController?.indexIsChanging != null &&
        tabController?.animation != null;
  }
}
