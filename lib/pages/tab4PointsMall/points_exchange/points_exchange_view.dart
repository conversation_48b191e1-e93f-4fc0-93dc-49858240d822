// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/points_exchange_model.dart';
import 'package:shoot_z/pages/tab4PointsMall/points_exchange/points_exchange_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///我的 积分兑换记录
///
class PointsExchangePage extends StatelessWidget {
  PointsExchangePage({super.key});
  final logic = Get.put(PointsExchangeLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.bg_color,
      // appBar:  MyAppBar(
      //   title: Text(S.current.integral_info1),
      // ),
      body: _teamInfoWidget(context),
    );
  }

  Widget _teamInfoWidget(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: 215.w, // 282.w,
          decoration: BoxDecoration(
              image: DecorationImage(
                  image: WxAssets.images.pointsDetailsBg.provider(),
                  fit: BoxFit.fill)),
        ),
        SizedBox(
          width: double.infinity,
          height: double.infinity, // 282.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                height: 50.w,
                alignment: Alignment.center,
                margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 60.w,
                      padding:
                          EdgeInsets.only(left: 8.w, right: 10.w, top: 8.w),
                      child: IconButton(
                          onPressed: () {
                            AppPage.back();
                          },
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                            size: 20,
                          )),
                    ),
                    Text(
                      S.current.integral_info2,
                      style: TextStyles.textBold16.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color: Colours.white),
                    ),
                    Container(
                      width: 60.w,
                      padding: EdgeInsets.only(left: 3.w, right: 10.w),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20.w,
              ),
              Expanded(
                child: _listWidget1(context),
              ),
            ],
          ),
        )
      ],
    );
  }

  /// 列表数据
  _listWidget1(BuildContext context) {
    return Obx(() {
      return SmartRefresher(
        controller: logic.refreshController,
        footer: buildFooter(),
        header: buildClassicHeader(),
        enablePullDown: true,
        enablePullUp: logic.dataList.isNotEmpty,
        onRefresh: () {
          logic.getdataList(isLoad: false, controller: logic.refreshController);
        },
        onLoading: () {
          logic.getdataList(controller: logic.refreshController);
        },
        physics: const AlwaysScrollableScrollPhysics(),
        //  physics: const NeverScrollableScrollPhysics(),
        child: (logic.dataFag["isFrist"] as bool)
            ? buildLoad()
            : logic.dataList.isEmpty
                ? SizedBox(
                    height: 480.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.no_order,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 105.w, height: 89.w),
                    ))
                : ListView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    padding: EdgeInsets.only(bottom: 40.w),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: logic.dataList.length,
                    itemBuilder: (context, position) {
                      return _listItemWidget(logic.dataList[position]);
                    }),
      );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(PointsExchangeModel item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 12.w),
      padding:
          EdgeInsets.only(left: 12.w, right: 12.w, top: 15.w, bottom: 15.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r), color: Colours.white),
      child: Row(
        children: [
          Expanded(
            child: RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                text: "${item.name ?? ""}",
                style: TextStyle(
                    //   color: Colours.white,
                    fontSize: 16.sp,
                    height: 1,
                    fontWeight: FontWeight.w500,
                    foreground: Paint()
                      ..shader = const LinearGradient(colors: [
                        Colours.color7B35ED,
                        Colours.colorA253EF
                      ]).createShader(Rect.fromLTWH(0, 0, 300, 0))),
              ),
            ),
          ),
          Text(
            item.date ?? "",
            style: TextStyles.regular.copyWith(
                fontSize: 12.sp,
                color: Colours.color999999,
                fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }
}
