///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PointsExchangeModel {
/*
{
  "name": "会员1天",
  "date": "2025-01-06 14:08:31",
  "type": 1
} 
*/

  String? name;
  String? date;
  int? type;

  PointsExchangeModel({
    this.name,
    this.date,
    this.type,
  });
  PointsExchangeModel.fromJson(Map<String, dynamic> json) {
    name = json['name']?.toString();
    date = json['date']?.toString();
    type = json['type']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['date'] = date;
    data['type'] = type;
    return data;
  }
}
