import 'dart:convert';

import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_logger.dart';
import 'package:fluwx/fluwx.dart';
import 'package:shoot_z/generated/l10n.dart';
import '../event_bus.dart';

sealed class MiniProgramPath {
  String getPath();
}

///比赛概况页
// class MatchOverview extends MiniProgramPath {
//   final String matchId;
//   MatchOverview({required this.matchId});
//   @override
//   String getPath() => "/pagesMatch/match/matchOverview?matchId=$matchId";
// }

///个人比赛数据报告页
// class PlayersDetails extends MiniProgramPath {
//   final String teamId;
//   final String matchId;
//   final String playerId;

//   PlayersDetails(
//       {required this.teamId, required this.matchId, required this.playerId});
//   @override
//   String getPath() =>
//       "/pagesMatch/match/playersDetails?teamId=$teamId&matchId=$matchId&playerId=$playerId";
// }

class FluwxUtils {
  static const miniProgramUserName = 'gh_04b5087f070b';
  static const appId = 'wx7917adc785bc0eaf';
  static const miniProgramAppId = 'wx4a1876ae0bb5f000';
  static final instance = FluwxUtils._();
  final Fluwx _fluwx = Fluwx();

  FluwxUtils._() {
    _fluwx.addSubscriber((response) {
      // 监听微信支付结果
      if (response is WeChatPaymentResponse) {
        if (response.isSuccessful) {
          BusUtils.instance.fire(EventAction(
              key: isUnlock ? EventBusKey.unlockResult : EventBusKey.payResult,
              action: true));
        } else {
          if (response.errStr != null) {
            WxLoading.showToast(response.errStr!);
          }
          BusUtils.instance.fire(EventAction(
              key: isUnlock ? EventBusKey.unlockResult : EventBusKey.payResult,
              action: false));
        }
      }
      // else if(response is WeChatShareResponse) {
      //   debug(response.isSuccessful);
      //   debug(response.errStr);
      //   debug(response.errCode);
      // }
    });
  }
  var isUnlock = false;
  Future<void> wxPay(String channelPayParams, {bool unlock = false}) async {
    if (await isWeChatNotInstalled()) return;
    isUnlock = unlock;

    ///获取参数
    Map<String, dynamic> result = jsonDecode(channelPayParams);
    await _fluwx.pay(
        which: Payment(
      appId: appId,
      partnerId: result['partnerId'].toString(),
      prepayId: result['prepayId'].toString(),
      packageValue: result['package'].toString(),
      nonceStr: result['nonceStr'].toString(),
      timestamp: int.parse(result['timeStamp']),
      sign: result['sign'].toString(),
    ));
  }

  Future<bool> isWeChatNotInstalled({bool showToast = true}) async {
    final install = await _fluwx.isWeChatInstalled;
    if (!install && showToast) {
      WxLoading.showToast(S.current.Please_install_wechat);
    }
    return !install;
  }

  void openMiniProgram({required MiniProgramPath pathType}) async {
    if (await isWeChatNotInstalled(showToast: false)) {
      WxLoading.showToast(S.current.Please_install_wechat_tips);
      return;
    }
    _fluwx
        .open(
            target: MiniProgram(
      username: miniProgramUserName,
      path: pathType.getPath(),
    ))
        .then((result) {
      debug('open $result');
    });
  }

  Future<bool> register() {
    return _fluwx.registerApi(
      appId: appId,
      universalLink:
          "https://f7b827c8aa8505f329cd379056b4ddc7.share2dlink.com/",
    );
  }

  Future<bool> share(WeChatShareModel model) {
    return _fluwx.share(model);
  }

  Future<bool> shareMiniPath(WeChatShareMiniProgramModel model) {
    return _fluwx.share(model);
  }
}
