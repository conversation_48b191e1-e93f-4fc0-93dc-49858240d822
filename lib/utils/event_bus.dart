class EventBusKey {
  /// 到首页
  static String toHome = "toHome";

  /// 回到集锦
  static String toCollectionHighlights = "toCollectionHighlights";

  /// 回到球馆主页
  static String toArenaDetails = "toArenaDetails";

  /// 初始化网络错误
  static String networkErr = "networkErr";

  /// 初始化网络恢复
  static String networkOk = "networkOk";

  static String loginSuccessful = "loginSuccessful";

  static String loginCanceled = "loginCanceled";

  static String payResult = "payResult";

  static String unlockResult = "unlockResult";

  static String getLocation = "getLocation";

  static String getCity = "getCity";

  static String receiveVip = "receiveVip";

  static String rankingsCitySwitch = "rankingsCitySwitch";

  static String rankingsRankTypeSwitch = "rankingsRankTypeSwitch";

  static String playerTabItemSwitch = "playerTabItemSwitch";

  static String teamTabItemSwitch = "teamTabItemSwitch";
  static String auditApplyTeam = "auditApplyTeam";
  static String teamChangeTab = "teamChangeTab";

  static String changePoints = "changePoints";
}
