class Routes {
  static const loading = "/loading";
  static const login = "/login";
  static const loginCode = "/loginCode";
  static const tab = "/tab";
  static const webview = "/webview";
  static const webviewh5 = "/webviewh5";
  static const notfound = "/notfound";
  static const userInfo = "/userInfo";
  static const hostSetting = "/hostSetting";
  static const modifyInfo = "/modifyInfo";
  static const videos = "/videos";
  static const videoPath = "/videoPath";
  static const highlightsVideo = "/highlightsVideo";
  static const place = "/place";
  static const selfieShotPage = "/selfieShotPage";
  static const selfieShotInfoPage = "/selfieShotInfoPage";
  static const selfieShotReportPage = "/selfieShotReportPage";
  static const selfieShotVideosPage = "/selfieShotVideosPage";
  static const creationWayPage = "/creationWayPage";
  static const placeSearch = "/placeSearch";
  static const sssy = "/sssy";
  static const arenaDetailsPage = "/arenaDetailsPage";
  static const pointsPage = "/pointsPage";
  static const pointsMallPage = "/pointsMallPage";
  static const pointsHelpPage = "/pointsHelpPage";
  static const pointsDetailsPage = "/pointsDetailsPage";
  static const comparisonPage = "/comparisonPage";
  static const shootCompositeVideoPage = "/ShootCompositeVideoPage";
  static const siteGoalPage = "/siteGoalPage";
  static const siteCompositeVideoPage = "/SiteCompositeVideoPage";
  static const shootGoalPage = "/ShootGoalPage";
  static const pointsExchangePage = "/pointsExchangePage";
  static const teamListPage = "/teamListPage";
  static const auditAddTeamPage = "/auditAddTeamPage";
  static const playersPage = "/playersPage";
  static const updateTeamMemberPage = "/updateTeamMemberPage";
  static const teamInfoPage = "/teamInfoPage";
  static const messageTypePage = "/messageTypePage";
  static const goodsInfoPage = "/goodsInfoPage";
  static const addressPage = "/addressPage";

  static const highlightsPage = "/HighlightsPage";
  static const featuredInfoPage = "/featuredInfoPage";
  static const addTeamPage = "/addTeamPage";
  static const messageInfoPage = "/messageInfoPage";
  static const messageListPage = "/messageListPage";
  static const playerReportPage = "/playerReportPage";
  static const unlockDataPage = "/unlockDataPage";
  static const competitionCouponsPage = "/competitionCouponsPage";
  static const ordersPage = "/ordersPage";
  static const couponsPage = "/couponsPage";
  static const couponsHistoryPage = "/couponsHistoryPage";
  static const optionGoalPage = "/optionGoalPage";
  static const optionPlayerGoalPage = "/optionPlayerGoalPage";
  static const optionGoalHelpPage = "/optionGoalHelpPage";
  static const optionSitePage = "/optionSitePage";
  static const siteReportPage = "/siteReportPage";
  static const compositeVideoPage = "/compositeVideoPage";
  static const compositePlayerVideoPage = "/compositePlayerVideoPage";
  static const vipPage = '/VipPage';
  static const inputInviteCodePage = '/inputInviteCodePage';
  static const inviteCodePage = '/inviteCodePage';
  static const materialLibraryPage = "/materialLibraryPage";
  static const kfPage = '/KfPage';
  static const settingsPage = '/SettingsPage';
  static const aboutPage = '/AboutPage';
  static const gameDetailsPage = '/GameDetailsView';
  static const teamReportPage = '/TeamReportPage';
  static const rankingsPage = '/RankingsPage';
  static const pointsGoodsTypePage = '/pointsGoodsTypePage';
  static const rankingRulesPage = '/RankingRulesPage';
  static const battleDetailPage = '/BattleDetailPage';
  static const battleDetailTeamPage = '/BattleDetailTeamPage';
  static const createBattlePage = '/CreateBattlePage';
  static const myBattlePage = '/MyBattlePage';
  static const purposeHistoryPage = '/PurposeHistoryPage';
  static const scheduleHomePage = '/ScheduleHomePage';
  static const competitionDetailPage = '/competitionDetailPage';
  static const competitionSignUpPage = '/competitionSignUpPage';
  static const myRegistrationPage = '/myRegistrationPage';
  static const competitionTeamsPage = '/competitionTeamsPage';
  static const careerHighlightsHomePage = '/careerHighlightsHomePage';
}
