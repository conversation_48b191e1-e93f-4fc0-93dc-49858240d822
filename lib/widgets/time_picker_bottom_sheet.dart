import 'dart:developer' as cc;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/widgets/time_picker_bottom_sheet_logic.dart';

///更多比赛
class TimePickerBottomSheet extends StatelessWidget {
   TimePickerBottomSheet({super.key});

  final logic = Get.put(TimePickerBottomSheetLogic());

  @override
  Widget build(BuildContext context) {
    Future.delayed(Duration.zero, () {
        logic.hourController.jumpToItem(logic.saveSelectHourIndex.value);
        logic.minuteController.jumpToItem(logic.saveSelectMinuteIndex.value);
    });
    return Container(
      height: 310,
      padding: EdgeInsets.only(
          top: 8, left: 10, right: 10, bottom: ScreenUtil().bottomBarHeight),
      decoration: BoxDecoration(
          color: const Color(0xff191921),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r))),
      child: Column(
        children: [
          Center(
            child: Container(
              width: 38,
              height: 4,
              decoration: BoxDecoration(
                  color: const Color(0x1AD8D8D8),
                  borderRadius: BorderRadius.all(Radius.circular(2.5.r))),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          // 顶部操作栏
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消',
                    style: TextStyle(color: Color(0xff5C5C6E), fontSize: 14)),
              ),
              const Text(
                '请选择比赛时间',
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white),
              ),
              TextButton(
                onPressed: () {
                  logic.saveSelectHourIndex.value = logic.selectedHourIndex.value;
                  logic.saveSelectMinuteIndex.value = logic.selectedMinuteIndex.value;
                  Navigator.pop(context,
                      '${logic.hours[logic.selectedHourIndex.value]}${logic.minutes[logic.selectedMinuteIndex.value]}');
                },
                child: const Text('保存',
                    style: TextStyle(fontSize: 14, color: Color(0xff922BFF))),
              ),
            ],
          ),

          // 时间选择器
          Expanded(
            child: Stack(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 小时选择
                    _buildPicker(
                      controller: logic.hourController,
                      items: logic.hours,
                      selectIndex: logic.selectedHourIndex,
                      onSelectedItemChanged: (index) {
                        // setState(() {
                        //   selectedHour = hours[index];
                        // });
                      },
                    ),
                    // const SizedBox(width: 24),

                    // 分钟选择
                    _buildPicker(
                      controller: logic.minuteController,
                      items: logic.minutes,
                      selectIndex: logic.selectedMinuteIndex,
                      onSelectedItemChanged: (index) {
                        // setState(() {
                        //   selectedMinute = minutes[index];
                        // });
                      },
                    ),
                  ],
                ),
                Positioned(
                    top: 78,
                    left: 30,
                    right: 30,
                    child: Container(
                      width: ScreenUtil().screenWidth - 60,
                      height: 1.w,
                      color: const Color(0xff2F2F3B),
                    )),
                Positioned(
                    top: 128,
                    left: 30,
                    right: 30,
                    child: Container(
                      width: ScreenUtil().screenWidth - 60,
                      height: 1.w,
                      color: const Color(0xff2F2F3B),
                    ))
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPicker({
    required FixedExtentScrollController controller,
    required RxList<String> items,
    required ValueChanged<int> onSelectedItemChanged,
    required RxInt selectIndex
  }) {
    return Obx(() {
      return SizedBox(
        width: 70,
        // color: Colors.red,
        child: ListWheelScrollView(
          controller: controller,
          useMagnifier: true, // 启用放大镜效果
          magnification: 1.067, // 放大倍数
          itemExtent: 50,
          physics: const FixedExtentScrollPhysics(),
          onSelectedItemChanged: (index) {
            selectIndex.value = index;
          },
          children: List.generate(items.length, (index) {
            // cc.log('!!!!!!!${selectIndex.value}$index');
            return Center(
              child: Text(
                items[index],
                style: TextStyle(
                    fontSize: 15,
                    color: index == selectIndex.value
                        ? Colors.white
                        : const Color(0xff9393A5)),
              ),
            );
          }),
        ),
      );
    });
  }
}
