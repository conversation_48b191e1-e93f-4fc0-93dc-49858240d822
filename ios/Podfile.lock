PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/no_pay (= 0.0.1)
  - fluwx/no_pay (0.0.1):
    - Flutter
    - OpenWeChatSDKNoPay (~> 2.0.4)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - GRDB.swift (6.24.1):
    - GRDB.swift/standard (= 6.24.1)
  - GRDB.swift/standard (6.24.1)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - map_launcher (0.0.1):
    - Flutter
  - MediaPipeTasksCommon (0.10.14)
  - MediaPipeTasksVision (0.10.14):
    - MediaPipeTasksCommon (= 0.10.14)
  - open_file_ios (0.0.1):
    - Flutter
  - OpenWeChatSDKNoPay (2.0.4)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - QCloudCore (6.4.7):
    - QCloudCore/Default (= 6.4.7)
  - QCloudCore/Default (6.4.7):
    - QCloudTrack/Beacon (= 6.4.7)
  - QCloudCOSXML (6.4.7):
    - QCloudCOSXML/Default (= 6.4.7)
  - QCloudCOSXML/Default (6.4.7):
    - QCloudCore (= 6.4.7)
  - QCloudTrack/Beacon (6.4.7)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SnapKit (5.7.1)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - tencentcloud_cos_sdk_plugin (1.2.3):
    - Flutter
    - QCloudCOSXML (= 6.4.7)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - GRDB.swift
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - map_launcher (from `.symlinks/plugins/map_launcher/ios`)
  - MediaPipeTasksVision (= 0.10.14)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - SnapKit (~> 5.7.0)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - tencentcloud_cos_sdk_plugin (from `.symlinks/plugins/tencentcloud_cos_sdk_plugin/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - GRDB.swift
    - MediaPipeTasksCommon
    - MediaPipeTasksVision
    - OpenWeChatSDKNoPay
    - OrderedSet
    - QCloudCore
    - QCloudCOSXML
    - QCloudTrack
    - SnapKit

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  map_launcher:
    :path: ".symlinks/plugins/map_launcher/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  tencentcloud_cos_sdk_plugin:
    :path: ".symlinks/plugins/tencentcloud_cos_sdk_plugin/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  connectivity_plus: 2256d3e20624a7749ed21653aafe291a46446fee
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_native_splash: 35ddbc7228eafcb3969dcc5f1fbbe27c1145a4f0
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  fluwx: 7330691eb49010b2b392434361a2526de339a396
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  GRDB.swift: 136dcb5d8dddca50aae3ba7d77475f79e7232cd8
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  map_launcher: fe43bda6720bb73c12fcc1bdd86123ff49a4d4d6
  MediaPipeTasksCommon: 5660099c2dd81f7ac4a7a5f51055785ead8e0e64
  MediaPipeTasksVision: 0fac0db83c0b45e4d7811a9227be5e571403cf83
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  OpenWeChatSDKNoPay: 84a2f83b3adbae0f1b55651141f477d20aa90a75
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  QCloudCore: 49e7571984a8e5fd8f0fd58c2f3b806255796223
  QCloudCOSXML: 7205e76aa9cf613468222615483c9d7c074e4298
  QCloudTrack: 3b53a7fc4fe3920e407f2aa73f2452992a61f7f3
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  tencentcloud_cos_sdk_plugin: c3d78b2c3bae42b5a20009dc047f3f1621e6272b
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556

PODFILE CHECKSUM: d1f2cf7676c68961d32e10c5206bb6b346bcb370

COCOAPODS: 1.16.2
