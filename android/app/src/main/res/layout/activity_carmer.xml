<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".MainActivity">

    <!-- 相机预览 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/preview_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.camera.view.PreviewView
        android:id="@+id/view_finder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:scaleType="fillStart"/>
    <com.shootZ.app.shoot_z.OverlayView
        android:id="@+id/overlay"
        android:layout_height="match_parent"
        android:layout_width="match_parent" />
    <!-- 空心矩形视图 -->
    <com.shootZ.app.shoot_z.utils.RectangleBorderView
        android:id="@+id/rectView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <!-- 投篮统计显示 -->
    <LinearLayout
        android:id="@+id/count_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/rounded_background"
        android:orientation="vertical"
        android:padding="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/count_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0/0"
            android:textColor="@android:color/white"
            android:textSize="22sp"
            android:textStyle="bold" />
    </LinearLayout>

    <!-- 推理时间显示 -->
    <ImageView
        android:id="@+id/inference_img_label"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/rounded_background"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <TextView
        android:id="@+id/inference_time_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="116dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/rounded_background"
        android:padding="8dp"
        android:text="推理时间: 0ms"
        android:visibility="gone"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 控制按钮区域 -->
    <LinearLayout
        android:id="@+id/control_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/start_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/button_background"
            android:padding="12dp"
            android:text="开始"
            android:textColor="@android:color/white"
            android:textSize="18sp" />

        <Button
            android:id="@+id/end_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/button_background"
            android:padding="8dp"
            android:text="关闭"
            android:textSize="18sp"
            android:textColor="@android:color/white"
             />
<!--        android:visibility="gone"-->
    </LinearLayout>

    <!-- 功能按钮区域 -->
    <LinearLayout
        android:id="@+id/function_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/show_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/button_background"
            android:padding="8dp"
            android:text="查看进球"
            android:visibility="gone"
            android:textColor="@android:color/white"
            android:textSize="20sp" />

        <Button
            android:id="@+id/record_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/button_background"
            android:padding="8dp"
            android:visibility="gone"
            android:text="比赛记录"
            android:textColor="@android:color/white"
            android:textSize="20sp" />

        <Button
            android:id="@+id/toggle_detection_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/button_background"
            android:padding="8dp"
            android:text="隐藏检测框"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/video_settings_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/button_background"
            android:padding="8dp"
            android:text="视频设置"
            android:visibility="gone"
            android:textColor="@android:color/white" />

    </LinearLayout>

    <!-- 进球详情视图 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/goal_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="32dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/goal_view_background"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 关闭按钮 -->
        <Button
            android:id="@+id/goal_view_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:text="⬇"
            android:textColor="@android:color/black"
            android:textSize="24sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 视频播放器占位 -->
        <View
            android:id="@+id/video_view"
            android:layout_width="350dp"
            android:layout_height="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="@android:color/darker_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/goal_view_close" />

        <!-- 进球记录列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/goals_recycler_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintBottom_toBottomOf="@id/video_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/video_view"
            app:layout_constraintTop_toTopOf="@id/video_view" />

        <!-- 统计标签 -->
        <TextView
            android:id="@+id/goal_view_count_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:text="0/0 （命中/投篮）"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            app:layout_constraintStart_toStartOf="@id/video_view"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/goal_view_count_label2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:text="0/0 （投篮人/投篮）"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 相机不可用提示 -->
    <TextView
        android:id="@+id/camera_unavailable_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="相机不可用"
        android:textColor="@android:color/white"
        android:textSize="17sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>