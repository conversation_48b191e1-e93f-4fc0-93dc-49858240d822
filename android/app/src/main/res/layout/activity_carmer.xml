<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".MainAcitvity">

    <!-- 相机预览 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/preview_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.camera.view.PreviewView
        android:id="@+id/view_finder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:scaleType="fillStart"/>
    <com.shootZ.app.shoot_z.OverlayView
        android:id="@+id/overlay"
        android:layout_height="match_parent"
        android:layout_width="match_parent" />
    <!-- 空心矩形视图 -->
    <com.shootZ.app.shoot_z.utils.RectangleBorderView
        android:id="@+id/rectView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        />
<!--    <ImageView-->
<!--        android:id="@+id/rectView2"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:scaleType="fitXY"-->
<!--        android:visibility="gone"-->
<!--        android:src="@mipmap/carmer_scan"/>-->
    <LinearLayout
        android:id="@+id/ll_carmer_tip"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/c_70000000"
        android:orientation="vertical"
        android:gravity="center_horizontal">
        <TextView
            android:id="@+id/tv_carmer_tip"
            android:layout_width="354dp"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:layout_marginTop="100dp"
            android:textColor="@color/white"
            android:text="1.篮筐需带篮网 \n2.手机摄像头逆光、球场较暗都会影响识别效果 \n3.手机保持静置稳定、不晃动，识别效果会更好 \n4.推荐使用支架固定"/>

        <Button
            android:id="@+id/bt_carmer_tip_know"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:background="@drawable/button_background3"
            android:paddingHorizontal="47dp"
            android:paddingVertical="17dp"
            android:text="知道了"
            android:textSize="14sp"
           android:textStyle="bold"
            android:textColor="@android:color/white"
            />

        <TextView
            android:id="@+id/tv_carmer_tip2"
            android:layout_width="354dp"
            android:layout_height="40dp"
            android:textSize="14sp"
            android:visibility="gone"
            android:gravity="center"
            android:textColor="@color/white"
            android:text="请将手机视野对准篮筐和球场位置"/>

        <Button
            android:id="@+id/bt_carmer_tip_start1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:visibility="gone"
            android:background="@drawable/button_background3"
            android:paddingHorizontal="47dp"
            android:paddingVertical="17dp"
            android:text="开始"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            />
    </LinearLayout>

    <ImageView
        android:id="@+id/carmer_close"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@mipmap/carmer_close"/>
    <!-- 推理时间显示 -->
    <ImageView
        android:id="@+id/inference_img_label"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        android:background="@drawable/rounded_background"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <TextView
        android:id="@+id/inference_time_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="116dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/rounded_background"
        android:padding="8dp"
        android:text="推理时间: 0ms"
        android:visibility="gone"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!-- 投篮统计显示 -->
    <TextView
        android:id="@+id/count_label"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0/0"
        android:textSize="20sp"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:layout_marginBottom="50dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
       />
    <!-- 功能按钮区域 -->
    <LinearLayout
        android:id="@+id/function_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:id="@+id/ll_start_button"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">
            <ImageView
                android:id="@+id/start_button"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:layout_marginBottom="11dp"
                android:src="@mipmap/carmer_pause"
                android:scaleType="fitXY"
                />
            <TextView
                android:id="@+id/start_button_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="开始"
                android:textColor="@color/white"
                android:textSize="14sp"
                />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/ll_end_button"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginLeft="15dp"
            android:orientation="vertical">
            <ImageView
                android:id="@+id/end_button"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:layout_marginBottom="11dp"
                android:src="@mipmap/carmer_over"
                android:scaleType="fitXY"
            />
            <TextView
                android:id="@+id/end_button_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="结束"
                android:textColor="@color/white"
                android:textSize="14sp"
                />
        </LinearLayout>


<!--        <Button-->
<!--                    android:id="@+id/toggle_detection_button"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginBottom="8dp"-->
<!--                    android:background="@drawable/button_background"-->
<!--                    android:padding="8dp"-->
<!--                    android:visibility="gone"-->
<!--                    android:text="隐藏检测框"-->
<!--                    android:textColor="@android:color/white" />-->
    </LinearLayout>

<!--    <LinearLayout-->
<!--        android:id="@+id/function_layout"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginEnd="16dp"-->
<!--        android:layout_marginBottom="16dp"-->
<!--        android:orientation="vertical"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent">-->

<!--        <Button-->
<!--            android:id="@+id/show_button"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="8dp"-->
<!--            android:background="@drawable/button_background"-->
<!--            android:padding="8dp"-->
<!--            android:text="查看进球"-->
<!--            android:visibility="gone"-->
<!--            android:textColor="@android:color/white"-->
<!--            android:textSize="20sp" />-->

<!--        <Button-->
<!--            android:id="@+id/record_button"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="8dp"-->
<!--            android:background="@drawable/button_background"-->
<!--            android:padding="8dp"-->
<!--            android:visibility="gone"-->
<!--            android:text="比赛记录"-->
<!--            android:textColor="@android:color/white"-->
<!--            android:textSize="20sp" />-->

<!--        <Button-->
<!--            android:id="@+id/toggle_detection_button"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="8dp"-->
<!--            android:background="@drawable/button_background"-->
<!--            android:padding="8dp"-->
<!--            android:visibility="gone"-->
<!--            android:text="隐藏检测框"-->
<!--            android:textColor="@android:color/white" />-->

<!--        <Button-->
<!--            android:id="@+id/video_settings_button"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:background="@drawable/button_background"-->
<!--            android:padding="8dp"-->
<!--            android:text="视频设置"-->
<!--            android:visibility="gone"-->
<!--            android:textColor="@android:color/white" />-->

<!--    </LinearLayout>-->

    <!-- 进球详情视图 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/goal_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="32dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/goal_view_background"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 关闭按钮 -->
        <Button
            android:id="@+id/goal_view_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:text="⬇"
            android:textColor="@android:color/black"
            android:textSize="24sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 视频播放器占位 -->
        <View
            android:id="@+id/video_view"
            android:layout_width="350dp"
            android:layout_height="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="@android:color/darker_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/goal_view_close" />

        <!-- 进球记录列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/goals_recycler_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintBottom_toBottomOf="@id/video_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/video_view"
            app:layout_constraintTop_toTopOf="@id/video_view" />

        <!-- 统计标签 -->
        <TextView
            android:id="@+id/goal_view_count_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:text="0/0 （命中/投篮）"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            app:layout_constraintStart_toStartOf="@id/video_view"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/goal_view_count_label2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:text="0/0 （投篮人/投篮）"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 相机不可用提示 -->
    <TextView
        android:id="@+id/camera_unavailable_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="相机不可用"
        android:textColor="@android:color/white"
        android:textSize="17sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>