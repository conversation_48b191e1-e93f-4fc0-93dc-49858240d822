package com.shootZ.app.shoot_z.utils

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View

class RectangleBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 矩形对象
    private val rect = RectF()

    // 边框画笔
    private val borderPaint = Paint().apply {
        color = Color.BLUE
        style = Paint.Style.STROKE
        strokeWidth = 8f
        isAntiAlias = true
    }

    // 背景画笔（可选）
    private val backgroundPaint = Paint().apply {
        color = Color.argb(30, 0, 0, 255) // 半透明蓝色
        style = Paint.Style.FILL
    }

    // 角半径（圆角矩形）
    private var cornerRadius = 0f

    // 设置矩形位置和大小
    fun setRect(left: Float, top: Float, right: Float, bottom: Float) {
        rect.set(left, top, right, bottom)
        invalidate() // 触发重绘
    }

    // 设置边框颜色
    fun setBorderColor(color: Int) {
        borderPaint.color = color
        invalidate()
    }

    // 设置边框宽度
    fun setBorderWidth(width: Float) {
        borderPaint.strokeWidth = width
        invalidate()
    }

    // 设置圆角半径
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

//        // 绘制背景（可选）
//        if (backgroundPaint.color != Color.TRANSPARENT) {
//            if (cornerRadius > 0) {
//                // 绘制圆角矩形背景
//                canvas.drawRoundRect(rect, cornerRadius, cornerRadius, backgroundPaint)
//            } else {
//                // 绘制直角矩形背景
//                canvas.drawRect(rect, backgroundPaint)
//            }
//        }

        // 绘制边框
        if (cornerRadius > 0) {
            // 绘制圆角矩形边框
            canvas.drawRoundRect(rect, cornerRadius, cornerRadius, borderPaint)
        } else {
            // 绘制直角矩形边框
            canvas.drawRect(rect, borderPaint)
        }
    }
}