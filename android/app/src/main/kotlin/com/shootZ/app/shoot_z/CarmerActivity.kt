package com.shootZ.app.shoot_z

import AIService
import android.Manifest.permission.WRITE_EXTERNAL_STORAGE
import android.annotation.SuppressLint
import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.PackageManager.PERMISSION_GRANTED
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.RectF
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Environment.DIRECTORY_PICTURES
import android.os.Environment.getExternalStoragePublicDirectory
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.provider.Settings
import android.util.DisplayMetrics
import android.util.Log
import android.util.Range
import android.util.Size
import android.util.SizeF
import android.view.Surface
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageAnalysis.OUTPUT_IMAGE_FORMAT_RGBA_8888
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.FileOutputOptions
import androidx.camera.video.Quality
import androidx.camera.video.QualitySelector
import androidx.camera.video.Recorder
import androidx.camera.video.Recording
import androidx.camera.video.VideoCapture
import androidx.camera.video.VideoRecordEvent
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.google.mediapipe.framework.image.MPImage
import com.google.mediapipe.tasks.vision.core.RunningMode
import com.shootZ.app.shoot_z.config.ShootzPlugin
import com.shootZ.app.shoot_z.config.VideoStorageManager
import com.shootZ.app.shoot_z.databinding.ActivityCarmerBinding
import com.shootZ.app.shoot_z.interfaces.AIServiceDelegate
import com.shootZ.app.shoot_z.interfaces.PermissionCallback
import com.shootZ.app.shoot_z.model.ShootEvent
import com.shootZ.app.shoot_z.model.ShootEventRecord
import com.shootZ.app.shoot_z.service.LogLevel
import com.shootZ.app.shoot_z.service.LogService
import com.shootZ.app.shoot_z.service.ObjectDetectorHelper
import com.shootZ.app.shoot_z.service.VideoRecordingService
import com.shootZ.app.shoot_z.utils.PermissionManager
import com.shootZ.app.shoot_z.utils.PermissionManager.Companion.areAllPermissionsGranted
import com.shootZ.app.shoot_z.utils.PermissionManager.Companion.getRequiredPermissions
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import java.lang.ref.WeakReference
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit


/**
 * CameraActivity - 负责相机预览和实时对象检测的Activity
 *
 * 核心功能：
 * 1. 管理相机生命周期和预览
 * 2. 设置并运行MediaPipe对象检测模型
 * 3. 处理检测结果并在UI上实时显示
 * 4. 处理设备配置变更和权限检查
 */

class CarmerActivity : AppCompatActivity(), ObjectDetectorHelper.DetectorListener,
    AIServiceDelegate {
    // AIService - 负责目标检测和进球检测
    private var aiService: AIService? = null

    // 日志标签
    private val TAG = "ObjectDetection"
    var videoResolution2: SizeF = SizeF(0f, 0f)

    // 视图绑定实例
    private lateinit var binding: ActivityCarmerBinding
    private var shouldDrawDetectionBoxes = true
    private lateinit var storageManager: VideoStorageManager //视频存储管理器
    private var tempVideoFile: File? = null //临时视频文件地址

    // 对象检测助手实例
    private lateinit var objectDetectorHelper: ObjectDetectorHelper

    // CameraX 组件实例
    private var preview: Preview? = null          // 预览用例
    private var imageAnalyzer: ImageAnalysis? = null // 图像分析用例
    private var camera: Camera? = null            // 相机实例
    private var cameraProvider: ProcessCameraProvider? = null // 相机提供者
    private lateinit var videoCapture: VideoCapture<Recorder>
    private lateinit var permissionManager: PermissionManager
    private val mainHandler = Handler(Looper.getMainLooper())
    private var delayedStopTask: Runnable? = null

    // 检测状态
    private enum class IdentifyStatus {
        NOT_STARTED, STARTED, PAUSED
    }

    // 当前会话的投篮记录
    private val currentShootRecords = mutableListOf<ShootEventRecord>()

    // 所有历史投篮记录，按会话分组
    private val allShootRecordsBySession = mutableMapOf<String, MutableList<ShootEventRecord>>()

    /** 用于执行阻塞性ML操作的后台执行器 */
    private lateinit var backgroundExecutor: ExecutorService
    private var trainingId: String? = null
    private var sampleUrl: String? = null

    /** 用于执行阻塞性ML操作的后台执行器 */
    private lateinit var threadPool: ExecutorService
    private lateinit var videoRecordingService: VideoRecordingService //视频录制页面
    private var identifyStatus = IdentifyStatus.NOT_STARTED
    private var startTime: String? = null
    private var idname: Long = 0

    //  private lateinit var dbManager: DatabaseManager //数据库操作投篮记录
    // 当数据产生时调用
    fun sendData(data: String) {
        runOnUiThread {
            plugin.sendDataFromCamera(data)
        }
    }

    private lateinit var plugin: ShootzPlugin
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //RxFFmpegInvoke.getInstance().setDebug(true);
        // 获取插件实例
        plugin = try {
            MainApplication.getInstance().plugin
        } catch (e: Exception) {
            Log.e("CameraActivity", "Failed to get plugin", e)
            return
        }
        storageManager = VideoStorageManager(applicationContext)
        storageManager.createTemporaryVideoFile()
        tempVideoFile = storageManager.getTemporaryVideoDirectory();
        // 获取参数
        trainingId = intent.getStringExtra("trainingId")
        sampleUrl = intent.getStringExtra("sampleUrl")
        Log.e("halfShootingRecording", "trainingId:${trainingId}-${sampleUrl}");
        // 设置全屏
        window.setFlags(
            android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN,
            android.view.WindowManager.LayoutParams.FLAG_FULLSCREEN
        )

        // 初始化权限管理器
        permissionManager = PermissionManager(this)
        binding = ActivityCarmerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        //   dbManager = DatabaseManager.getInstance(this)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        WindowCompat.getInsetsController(window, window.decorView)?.apply {
            hide(WindowInsetsCompat.Type.systemBars())
            systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }

        // 适配刘海屏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            window.attributes.layoutInDisplayCutoutMode =
                WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
        }
//        val myDelegate = MyLogDelegate()
//        LogService.addDelegate(myDelegate)

        // 初始化单线程后台执行器（用于运行计算密集型任务）
        backgroundExecutor = Executors.newSingleThreadExecutor()
        threadPool = Executors.newSingleThreadExecutor()
        // 在后台线程中创建并初始化对象检测助手
        backgroundExecutor.execute {
            // 实例化对象检测助手
            objectDetectorHelper = ObjectDetectorHelper(
                context = applicationContext,
                threshold = 0.5f, // 默认阈值
                currentDelegate = ObjectDetectorHelper.DELEGATE_CPU, // 默认CPU代理
                currentModel = ObjectDetectorHelper.MODEL_EFFICIENTDETV0, // 默认模型
                maxResults = 3, // 默认最大结果数
                objectDetectorListener = this, // 设置结果回调接口
                runningMode = RunningMode.LIVE_STREAM // 使用实时流模式
            )
        }
        // 设置叠加层为实时流模式（用于在相机预览上绘制检测框）
        binding.overlay.setRunningMode(RunningMode.LIVE_STREAM)
        // 设置矩形位置和大小（示例值）
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        val screenWidth = displayMetrics.widthPixels.toFloat()
        val screenHeight = displayMetrics.heightPixels.toFloat()
        initViews()
        // 初始化视频录制服务
        videoRecordingService = VideoRecordingService(this)
        videoRecordingService.setVideoResolution(1920, 1080)
        videoRecordingService.setShouldSaveGoalVideos(true)
        videoRecordingService.setShouldSaveNoGoalVideos(true)
        // 设置文件路径回调
        videoRecordingService.filePathSuccessBlock = { filePath, shootEventRecord ->
            runOnUiThread {
                // 处理保存的视频文件路径
                //  saveVideoToGallery(filePath)
                Log.e(TAG, "halfShootingRecording: " + filePath + "\n" + shootEventRecord)
                shootEventRecord.filePath = filePath
                sendData(shootEventRecord.toJson());
                //    {"player_image_uploaded":false,"player_confidence":0,"file_path":
                //"file:\/\/\/private\/var\/mobile\/Containers\/Data\/Application\/F9F6A010-24E4-44FC-A2A8-212C0E1C9F
                //BB\/tmp\/cache_20250515_144850.mp4","training_id":"5","start_time":"2025-05-15 14:44:01",
                //"is_goal":true,"goal_time":1747291729.2113008,"file_path_uploaded":false,
                //"created_at":768984531.00349796}
            }
        }
        // 初始化AIService
        aiService = AIService(this);
        aiService?.delegate = this;
        //aiService?.startDetection()
        Log.e(TAG, "MainActivity onCreate: AIService 实例化完成")
        // 请求权限
        // requestPermissions()
        requestPermissionsNative();
    }


    // 在 Activity 或 Fragment 中实现
    private val REQUEST_CODE_PHOTO_LIBRARY1 = 1001
    private val REQUEST_CODE_PHOTO_LIBRARY2 = 1002

    fun requestPermissionsNative() {
        val requiredPermissions = getRequiredPermissions()

        // 检查是否所有权限都已授予
        if (areAllPermissionsGranted(this)) {
            Log.e(TAG, "onPermissionsChecked:5所有权限已授予，启动相机")
            LogService.info("权限确认完成，开始初始化相机...")
            startCamera()
            return
        }

        // 请求权限
        ActivityCompat.requestPermissions(
            this,
            requiredPermissions,
            REQUEST_CODE_PHOTO_LIBRARY2
        )
    }

    // 在Activity中处理结果
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_CODE_PHOTO_LIBRARY2) {
            val allGranted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }

            if (allGranted) {
                Log.e(TAG, "onPermissionsChecked:5所有权限已授予，启动相机")
                LogService.info("权限确认完成，开始初始化相机...")
                startCamera()
            } else {
                val deniedPermissions = permissions.filterIndexed { index, _ ->
                    grantResults[index] != PackageManager.PERMISSION_GRANTED
                }
                val permanentlyDenied = deniedPermissions.any { permission ->
                    !ActivityCompat.shouldShowRequestPermissionRationale(this, permission)
                }
                Log.e(
                    TAG,
                    "onPermissionsChecked:6权限被拒绝: $deniedPermissions, 永久拒绝: $permanentlyDenied",
                )
                val deniedNames = deniedPermissions.map { permission ->
                    PermissionManager.getPermissionDisplayName(permission)
                }.joinToString("、")

                LogService.warning("权限被拒绝: $deniedNames")

                permissionManager.showPermissionDeniedDialog(
                    deniedPermissions = deniedPermissions,
                    permanentlyDenied = permanentlyDenied,
                    onRetry = {
                        Log.d(TAG, "用户选择重试权限请求")
                        requestPermissions()
                    },
                    onCancel = {
                        Log.w(TAG, "用户取消权限请求，关闭应用")
                        LogService.warning("权限不足，应用无法正常工作")
                        showExitDialog()
                    }
                )
            }
        } else if (requestCode == REQUEST_CODE_PHOTO_LIBRARY1) {
            if ((grantResults.isNotEmpty() &&
                        grantResults[0] == PackageManager.PERMISSION_GRANTED)
            ) {
                Log.e(TAG, "相册访问权限已授权")
            } else {
                Log.w(TAG, "相册访问权限被拒绝，视频无法保存到相册")
                showPhotoLibraryPermissionDeniedDialog()
            }
            return
        }
    }

    private fun requestPermissions() {

        Log.e(TAG, "onPermissionsChecked:0requestPermissions")
        permissionManager.requestPermissions(object : PermissionCallback {
            override fun onAllPermissionsGranted() {
                Log.e(TAG, "onPermissionsChecked:5所有权限已授予，启动相机")
                LogService.info("权限确认完成，开始初始化相机...")
                startCamera()
            }

            override fun onPermissionsDenied(
                deniedPermissions: List<String>,
                permanentlyDenied: Boolean
            ) {
                Log.e(
                    TAG,
                    "onPermissionsChecked:6权限被拒绝: $deniedPermissions, 永久拒绝: $permanentlyDenied",
                )
                val deniedNames = deniedPermissions.map { permission ->
                    PermissionManager.getPermissionDisplayName(permission)
                }.joinToString("、")

                LogService.warning("权限被拒绝: $deniedNames")

                permissionManager.showPermissionDeniedDialog(
                    deniedPermissions = deniedPermissions,
                    permanentlyDenied = permanentlyDenied,
                    onRetry = {
                        Log.d(TAG, "用户选择重试权限请求")
                        requestPermissions()
                    },
                    onCancel = {
                        Log.w(TAG, "用户取消权限请求，关闭应用")
                        LogService.warning("权限不足，应用无法正常工作")
                        showExitDialog()
                    }
                )
            }

            override fun onPermissionError(error: String) {
                LogService.error("权限请求失败: $error")
                Log.e(TAG, "onPermissionsChecked:7权限请求错误: $error ")
                AlertDialog.Builder(this@CarmerActivity)
                    .setTitle("权限错误")
                    .setMessage("权限请求过程中发生错误：\n$error\n\n请重启应用重试。")
                    .setPositiveButton("重试") { _, _ ->
                        requestPermissions()
                    }
                    .setNegativeButton("退出") { _, _ ->
                        finish()
                    }
                    .setCancelable(false)
                    .show()
            }
        })
    }

    /**
     * 显示退出确认对话框
     */
    private fun showExitDialog() {
        AlertDialog.Builder(this)
            .setTitle("无法继续")
            .setMessage("篮球检测应用需要摄像头、麦克风和存储权限才能正常工作。\n\n没有这些权限，应用将无法提供完整功能。")
            .setPositiveButton("重新授权") { _, _ ->
                requestPermissions()
            }
            .setNegativeButton("退出应用") { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }


    // 显示相册权限被拒绝的对话框
    private fun showPhotoLibraryPermissionDeniedDialog() {
        AlertDialog.Builder(this)
            .setTitle("相册访问被拒绝")
            .setMessage("保存视频需要访问您的相册。您可以在设置中更改此权限。")
            .setNegativeButton("取消") { dialog, _ -> dialog.dismiss() }
            .setPositiveButton("设置") { _, _ ->
                // 打开应用设置页面
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", packageName, null)
                }
                startActivity(intent)
            }
            .create()
            .show()
    }

    override fun onResume() {
        super.onResume()
        // 如果检测器已关闭则重新设置
        if (::objectDetectorHelper.isInitialized) {
            backgroundExecutor.execute {
                if (objectDetectorHelper.isClosed()) {
                    objectDetectorHelper.setupObjectDetector()
                }
            }
        }
    }

    private fun startCamera() {
        Log.e(TAG, "startCamera 调用")

        // 获取相机提供者的Future对象
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        // 添加监听器，当Future完成时执行相机初始化
        cameraProviderFuture.addListener(
            {
                try {
                    // 获取相机提供者实例
                    cameraProvider = cameraProviderFuture.get()
                    // 构建并绑定相机用例
                    bindCameraUseCases()
                } catch (exc: Exception) {
                    // 处理可能的异常
                    Log.e(TAG, "Camera provider future failed", exc)
                }
            },
            ContextCompat.getMainExecutor(this) // 使用主线程执行器
        )

        aiService?.startDetection()

    }

    override fun onPause() {
        super.onPause()
        // 如果对象检测器已初始化，保存设置并释放资源
        if (::objectDetectorHelper.isInitialized) {
            // 这里可以添加保存设置的逻辑（如果需要）
            // 关闭对象检测器并释放资源
            backgroundExecutor.execute {
                objectDetectorHelper.clearObjectDetector()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 发送关闭消息
        try {
            MainApplication.getInstance().plugin.didClosePage("channel_closed1")
        } catch (e: Exception) {
            Log.e("CameraActivity", "Failed to send close event", e)
        }
        // 关闭后台执行器
        backgroundExecutor.shutdown()
        // 等待执行器终止
        backgroundExecutor.awaitTermination(
            Long.MAX_VALUE,
            TimeUnit.NANOSECONDS
        )
        // 关闭后台执行器
        threadPool.shutdown()
        // 等待执行器终止
        threadPool.awaitTermination(
            Long.MAX_VALUE,
            TimeUnit.NANOSECONDS
        )
        //  videoRecordingService.release()
        // dbManager.close()
        // aiService?.release()
    }


    /**
     * 声明并绑定预览、捕获和分析用例
     */
    @SuppressLint("UnsafeOptInUsageError")
    private fun bindCameraUseCases() {

        // 获取相机提供者（非空）
        val cameraProvider = cameraProvider ?: throw IllegalStateException("相机初始化失败")
        // 相机选择器 - 默认使用后置相机
        val cameraSelector = CameraSelector.Builder()
            .requireLensFacing(CameraSelector.LENS_FACING_BACK).build()

        // 在重新绑定前解除所有已绑定的用例
        cameraProvider.unbindAll()
        // 修复点：安全获取屏幕旋转
        val rotation = when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                // 使用新的 API 获取旋转
                display?.rotation ?: Surface.ROTATION_0
            }

            else -> {
                // 使用旧 API，但添加空检查
                @Suppress("DEPRECATION")
                windowManager?.defaultDisplay?.rotation ?: Surface.ROTATION_0
            }
        }
        //binding.viewFinder.display.rotation
        Log.e(TAG, "bindCameraUseCases: " + rotation)
        preview = Preview.Builder()
            //  .setTargetAspectRatio(AspectRatio.RATIO_4_3) // 设置4:3宽高比
            .setTargetRotation(rotation) // 匹配当前显示旋转
            .setTargetResolution(Size(1920, 1080))
            .setTargetFrameRate(Range(30, 30)) // 设置帧率
            .build()

        // 图像分析用例配置 - 用于处理每一帧图像
        imageAnalyzer = ImageAnalysis.Builder()
            //   .setTargetAspectRatio(AspectRatio.RATIO_4_3) // 4:3比例
            .setTargetRotation(rotation) // 匹配旋转
            .setTargetResolution(Size(1920, 1080))
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST) // 仅保留最新帧
            .setOutputImageFormat(OUTPUT_IMAGE_FORMAT_RGBA_8888) // RGBA格式（匹配模型输入要求）
            .build()
            .also {
                // 设置分析器 - 使用对象检测助手处理每一帧
                it.setAnalyzer(
                    backgroundExecutor, // 后台执行器线程
                    objectDetectorHelper::detectLivestreamFrame // 帧处理函数引用
                )
            }
        // 视频录制用例
        val qualitySelector = QualitySelector.from(Quality.HD) //fHD
        val recorder = Recorder.Builder()
            .setQualitySelector(qualitySelector)
            .build()
        videoCapture = VideoCapture.withOutput(recorder)
        videoCapture.targetRotation = rotation  // 设置帧率


        try {
            // 绑定生命周期和用例到相机
            camera = cameraProvider.bindToLifecycle(
                this, // 绑定到Activity生命周期
                cameraSelector, // 相机选择（后置）
                preview,       // 预览用例
                imageAnalyzer,  // 图像分析用例
                videoCapture
            )
            preview?.setSurfaceProvider(binding.viewFinder.surfaceProvider)
        } catch (exc: Exception) {
            // 处理用例绑定失败
            Log.e(TAG, "Use case binding failed", exc)
            Toast.makeText(this, "Failed to bind camera use cases", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // 更新图像分析器的目标旋转方向，确保检测框方向正确
        val rotation = when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                // 使用新的 API 获取旋转
                display?.rotation ?: Surface.ROTATION_0
            }

            else -> {
                // 使用旧 API，但添加空检查
                @Suppress("DEPRECATION")
                windowManager?.defaultDisplay?.rotation ?: Surface.ROTATION_0
            }
        }
        imageAnalyzer?.targetRotation = rotation
    }

    /**
     * 对象检测成功回调 现在已经获得了篮板 篮球 投篮人等对象检测数据
     */
    var a = 0;
    override fun onResults(
        resultBundle: ObjectDetectorHelper.ResultBundle,
        input: MPImage,
        bitmap: Bitmap
    ) {
//

        runOnUiThread {
//            a++;
//            if(a%20==0){
//                universalSaveImage(bitmap)
//            }
            // 获取检测结果（实时流模式每次只有一个结果）
            val detectionResult = resultBundle.results[0]
            Log.e(TAG, "detectionResult333: " + detectionResult)
            Log.e(
                TAG,
                "detectionResult333: " + resultBundle.inputImageHeight + " " + resultBundle.inputImageWidth + " " + resultBundle.inputImageRotation
            )
            // 将结果传递给OverlayView进行绘制
            binding.overlay.setResults(
                detectionResult,
                resultBundle.inputImageHeight,
                resultBundle.inputImageWidth,
                resultBundle.inputImageRotation
            )
            if (videoResolution2.height <= 0 || videoResolution2.width < 0) {
                var height2 = minOf(input.width, input.height)
                var width2 = maxOf(input.width, input.height)
                videoResolution2 = SizeF(width2.toFloat(), height2.toFloat())
                // 向AIService设置视频分辨率
                aiService?.setVideoResolution2(videoResolution2)
            }
            // 将帧传递给AIService处理
            if (!bitmap.isRecycled) {
                aiService?.processVideoFrame(bitmap)
            }
            aiService?.onDetectionFinished(resultBundle)

            // 强制重绘叠加层（显示新的检测框）
            if (shouldDrawDetectionBoxes) {
                binding.overlay.clear()
            } else {
                if (resultBundle.inputImageWidth <= 0 || resultBundle.inputImageHeight <= 0) {
                    // 清除覆盖视图
                    binding.overlay.clear()
                } else {
                    binding.overlay.invalidate()
                }
            }

            // 同时将帧传递给视频录制服务

//            if(identifyStatus== IdentifyStatus.STARTED){
//                videoRecordingService.processVideoFrame(outputStream.toByteArray(), VideoRecordingService.FRAME_RATE, input.width, input.height)
//            }

        }
    }

    fun universalSaveImage(bitmap: Bitmap): String? {
        // Android 10+ 使用 MediaStore
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            return saveImageUsingMediaStore(applicationContext, bitmap)
        }
        // Android 6.0+ 需要权限
        else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(WRITE_EXTERNAL_STORAGE) == PERMISSION_GRANTED) {
                return saveToPublicFolderLegacy(bitmap)
            }
        }
        // 旧版 Android 无需请求权限
        else {
            return saveToPublicFolderLegacy(bitmap)
        }
        return null
    }

    /**
     * 使用 MediaStore API 保存图片（Android 10+）
     */
    private fun saveImageUsingMediaStore(context: Context, bitmap: Bitmap): String? {
        val resolver: ContentResolver = context.contentResolver
        val fileName = "IMG_${System.currentTimeMillis()}.jpg"

        val contentValues = ContentValues().apply {
            put(MediaStore.Images.Media.DISPLAY_NAME, fileName)
            put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
                put(MediaStore.Images.Media.IS_PENDING, 1)
            }
        }

        var uri: Uri? = null
        var outputStream: OutputStream? = null

        try {
            uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                ?: throw IOException("Failed to create MediaStore record")

            outputStream = resolver.openOutputStream(uri)
                ?: throw IOException("Failed to get output stream")

            if (!bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)) {
                throw IOException("Failed to compress bitmap")
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                contentValues.clear()
                contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                resolver.update(uri, contentValues, null, null)
            }

            sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri))
            // Toast.makeText(context, "图片已保存到相册", Toast.LENGTH_SHORT).show()

            // 返回包含文件名的完整路径
            val picturesDir =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
            val fullPath = File(picturesDir, fileName).absolutePath
            return fullPath

        } catch (e: IOException) {
            Log.e("SaveImage", "保存图片失败", e)
            uri?.let { resolver.delete(it, null, null) }
            Toast.makeText(context, "保存失败: ${e.message}", Toast.LENGTH_SHORT).show()
            return null
        } finally {
            outputStream?.close()
        }
    }

    @Suppress("DEPRECATION")
    private fun saveToPublicFolderLegacy(bitmap: Bitmap): String? {
        val directory = getExternalStoragePublicDirectory(DIRECTORY_PICTURES)
        directory.mkdirs()
        val file = File(directory, "IMG_${System.currentTimeMillis()}.jpg")

        try {
            FileOutputStream(file).use { stream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 95, stream)
            }
            sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file)))
            return file.absolutePath // 返回绝对路径
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * 检测错误回调
     */
    override fun onError(error: String, errorCode: Int) {
        runOnUiThread {
            Toast.makeText(this, error, Toast.LENGTH_SHORT).show()
            if (errorCode == ObjectDetectorHelper.GPU_ERROR) {
                Log.e(TAG, "GPU Error: $error")
                // 这里可以添加回退到CPU模式的逻辑
            }
        }
    }

    //    /// 当检测到篮筐和篮板区域并满足条件时调用（对应Swift的didDetectHoopAndBackboard）
    var a1 = 0;
    override fun didDetectHoopAndBackboard(
        hoopRect: RectF,
        backboardRect: RectF,
        isValid: Boolean
    ) {
        Log.e(TAG, "didDetectHoopAndBackboard1" + isValid)
        if (a1 % 5 == 0) {
            if (identifyStatus == IdentifyStatus.NOT_STARTED) {
                if (isValid) {
               //     binding.startButton.setBackgroundResource(R.drawable.button_background)
                    binding.btCarmerTipStart1.setBackgroundResource(R.drawable.button_background3)
                } else {
               //     binding.startButton.setBackgroundResource(R.drawable.button_background2)
                    binding.btCarmerTipStart1.setBackgroundResource(R.drawable.button_background3_hui)
                }
            }
        }
        a++
    }

    /// 当检测到投篮事件时调用（对应Swift的didDetectShootEvent）
    override fun didDetectShootEvent(event: ShootEvent) {
        Log.e(TAG, "didDetectShootEvent1111110" + event)

        // 使用弱引用避免内存泄漏
        val weakThis = WeakReference(this)

        // 在主线程更新投篮人图像
        Handler(Looper.getMainLooper()).post {
            if (event.playerImage != null && !event.playerImage.isRecycled && event.playerImage.width > 0 && event.playerImage.height > 0) {
                weakThis.get()?.binding?.inferenceImgLabel?.setImageBitmap(event.playerImage)
            }
        }
        // 构建详细的事件信息
        val eventInfo = StringBuilder()
        // 判断是否进球（基于 shotType）
        val isGoal = (event.shotType == "goal")
        // 添加进球信息
        if (isGoal) {
            eventInfo.append("[🏀进球]")
            event.goalTimestamp?.let {
                eventInfo.append(" (时间: ${"%.2f".format(it.toFloat() * 100)}%)")
            }
        } else {
            eventInfo.append("[😈未进球]")
        }

        // 添加投篮人信息
        if (event.playerImage != null) {
            eventInfo.append(" ⛹️: ${"%.2f".format(event.playerConfidence * 100)}%")
        } else {
            eventInfo.append(" ⛹️❓")
        }

        // 使用 LogService 记录日志
        LogService.log(
            eventInfo.toString(),
            level = if (isGoal) LogLevel.INFO else LogLevel.WARNING
        )

        // 保存投篮事件到数组和数据库
        startTime?.let { startTime ->
            var playerImagePath: String? = null
            Log.e(TAG, "processShootClassifierEvent1: " + event)
            Handler(Looper.getMainLooper()).post {
                // 检查 Bitmap 是否有效
                if (event.playerImage != null && !event.playerImage.isRecycled && event.playerImage.width > 0 && event.playerImage.height > 0) {
                    runOnUiThread { binding.inferenceImgLabel.setImageBitmap(event.playerImage) }
                }

            }
            // 如果有投篮人图像，保存到文件系统
            event.playerImage?.let { bitmap ->
                // 检查 Bitmap 是否有效
                if (!bitmap.isRecycled && bitmap.width > 0 && bitmap.height > 0) {
                    playerImagePath = universalSaveImage(bitmap)
                    Log.e(TAG, "保存图片路径111: $playerImagePath")
                }
            }
            idname++;
            // 创建记录
            var ss3 = 0.0;
            if (event.goalTimestamp == null) {
                ss3 = 0.0;
            } else {
                ss3 = event.goalTimestamp.toDouble();
            }
            val record = ShootEventRecord(
                id = idname,
                startTime = startTime,
                shootTime = event.startTimestamp.toDouble(), // 使用 startTimestamp
                isGoal = isGoal,
                goalTime = ss3,   // 使用 goalTimestamp
                playerImagePath = playerImagePath,
                playerConfidence = event.playerConfidence.toDouble(),
                playerImageUploaded = false,
                filePathUploaded = false,
                trainingId = trainingId!!,
                filePath = "",
                createdAt = System.currentTimeMillis().toDouble()
            )

            Log.e(TAG, "didDetectShootEvent1111110: " + playerImagePath)
            // 添加到当前会话记录数组（位置 0）
            currentShootRecords.add(0, record)
            // 同时保存到数据库（在后台线程）
//            threadPool.execute {
//           //     dbManager.saveShootEvent(event, startTime)
//            }
            // 保存视频 - 使用新的视频录制服务
            Handler(Looper.getMainLooper()).postDelayed({
                recording?.stop()
//                if (identifyStatus == IdentifyStatus.STARTED) {
//                    startRecording()
//                }
                videoRecordingService?.saveShootEventVideo2(isGoal, record)
            }, 2500)
            // 在主线程更新 UI
            Handler(Looper.getMainLooper()).post {
                weakThis.get()?.let { context ->
                    // 刷新集合视图
                    // context.binding?.collectionView?.adapter?.notifyDataSetChanged()
                    // 更新计数标签
                    updateCountLabels(context)
                }
            }
        } ?: LogService.error("错误：未找到开始时间，无法保存投篮事件")
    }

    private fun updateCountLabels(context: Context) {
        val totalCount = currentShootRecords.size
        val goalCount = currentShootRecords.count { it.isGoal }
        val playerDetectedCount = currentShootRecords.count { it.playerImagePath != null }

        binding?.countLabel?.text = "$goalCount/$totalCount"
        binding?.goalViewCountLabel?.text = "$goalCount/$totalCount （命中/投篮）"
        binding?.goalViewCountLabel2?.text = "$playerDetectedCount/$totalCount （投篮人/投篮）"
    }

    /// 当检测到目标并完成处理时调用（对应Swift的aiService(_:didDetectObjects:imageSize:)）
    override fun onObjectsDetected(
        result: ObjectDetectorHelper.ResultBundle?,
        imageSize: SizeF
    ) {
        Log.e(TAG, "onObjectsDetected:3 ")
    }

    /// 当需要显示或隐藏篮筐合法区域时调用（对应Swift的shouldDisplayHoopRegion
    override fun shouldDisplayHoopRegion(region: RectF, imageSize: SizeF, type: Int) {
        Log.e(TAG, "shouldDisplayHoopRegion4 " + region)
        Log.e(TAG, "shouldDisplayHoopRegion4 " + imageSize + "type" + type)
        if (region.isEmpty) {
            binding.rectView.visibility = View.GONE
        } else {
            binding.rectView.visibility = View.VISIBLE
            // 设置矩形位置和大小（示例值）
            val displayMetrics = DisplayMetrics()
            windowManager.defaultDisplay.getMetrics(displayMetrics)
            val screenWidth = displayMetrics.widthPixels.toFloat()
            val screenHeight = displayMetrics.heightPixels.toFloat()
            // 计算矩形位置（屏幕中央）
            val scale = screenWidth / imageSize.width
            val showHeight = imageSize.height * scale
            val cutY = showHeight - screenHeight  // 被裁切的上边高度

            // region 是原图坐标
            val left = region.left * scale
            val right = region.right * scale
            val top = region.top * scale
            val bottom = region.bottom * scale //-cutY

            // 计算宽度和高度
            val width = right - left
            val height = bottom - top

//            // 设置布局参数
//            val params =  binding.rectView2.layoutParams
//            params.width = width.toInt()
//            params.height = height.toInt()
//            binding.rectView2.layoutParams = params
//
//            // 直接设置位置
//            binding.rectView2.layout(left.toInt(), top.toInt(), right.toInt(), bottom.toInt())
//                // 应用新参数
//            binding.rectView2.layoutParams = params
         //    设置矩形
         //   binding.rectView.setImageBitmap()
            binding.rectView.setRect(left, top, right, bottom)
            // 自定义样式
            binding.rectView.setBorderColor(Color.WHITE)
            binding.rectView.setBorderWidth(8f)
            binding.rectView.setCornerRadius(12f)
            Log.e(
                TAG,
                "shouldDisplayHoopRegion4 left" + left + " top" + top + " right" + right + " bottom" + bottom + " screenHeight" + screenHeight + " screenWidth" + screenWidth
            )
        }
        Log.e(TAG, "shouldDisplayHoopRegion4 " + region + imageSize)

    }

    // 当截取到篮筐区域图像时调用（对应Swift的didCaptureHoopImage）
    override fun didCaptureHoopImage(image: Bitmap) {
        Log.e(TAG, "didCaptureHoopImage:5")
        // 检查 Bitmap 是否有效
        if (image != null && !image.isRecycled && image.width > 0 && image.height > 0) {
            runOnUiThread { binding.inferenceImgLabel.setImageBitmap(image) }
        }
    }


    private fun initViews() {
        binding.apply {
            startButton.setOnClickListener { onStartClick() }
            endButton.setOnClickListener { onEndClick() }
         //   showButton.setOnClickListener { onShowClick() }
          //  toggleDetectionButton.setOnClickListener { onToggleDetectionClick() }
            goalViewClose.setOnClickListener { onCloseGoalViewClick() }

            btCarmerTipKnow.setOnClickListener {
                rectView.visibility = View.VISIBLE
                tvCarmerTip.visibility = View.INVISIBLE
                btCarmerTipStart1.visibility = View.VISIBLE
                btCarmerTipKnow.visibility = View.GONE
                tvCarmerTip2.visibility = View.VISIBLE
            }
            btCarmerTipStart1.setOnClickListener {

                if (aiService?.hasValidHoopAndBackboard == false) {
                }else{
                    countLabel.visibility= View.VISIBLE
                    llCarmerTip.visibility= View.GONE
                    tvCarmerTip2.visibility = View.GONE
                    functionLayout.visibility = View.VISIBLE
                    onStartClick()
                }
            }


            carmerClose.setOnClickListener { onEndClick() }
        }
    }


    // 按钮点击事件处理
    private fun onStartClick() {
        if (aiService?.hasValidHoopAndBackboard == false) {
            Toast.makeText(this, "未检测到投篮框", Toast.LENGTH_SHORT).show()
            return;
        }
        when (identifyStatus) {
            IdentifyStatus.NOT_STARTED -> {
                identifyStatus = IdentifyStatus.STARTED
                startTime = System.currentTimeMillis().toString()
                binding.startButtonText.text = "暂停"
                binding.startButton.setImageResource(R.mipmap.carmer_pause); // 更改为新的资源
                binding.endButton.visibility = View.VISIBLE
                binding.endButtonText.text = "结束"
                //   viewModel.startNewSession()
                LogService.info("开始检测投篮")
                aiService?.startDetection()
                startRecording()
            }

            IdentifyStatus.STARTED -> {
                identifyStatus = IdentifyStatus.PAUSED
                binding.startButtonText.text = "继续"
                binding.startButton.setImageResource(R.mipmap.carmer_play); // 更改为新的资源
                LogService.info("暂停检测")
                aiService?.pauseDetection()
                recording?.stop()
            }

            IdentifyStatus.PAUSED -> {
                identifyStatus = IdentifyStatus.STARTED
                binding.startButtonText.text = "暂停"
                binding.startButton.setImageResource(R.mipmap.carmer_pause); // 更改为新的资源
                LogService.info("继续检测")
                aiService?.resumeDetection()
                startRecording()
            }
        }
    }

    private var recording: Recording? = null


    fun startRecording() {
        // 先移除之前的延迟任务，防止多次 stop
        delayedStopTask?.let { mainHandler.removeCallbacks(it) }
        // 如果已经在录制，直接返回，不再重复 start
        if (recording != null) {
            Log.w("VideoCapture", "录制已在进行，不能重复开始")
            return
        }
        val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
        val fileName = "cache_${dateFormat.format(Date())}.mp4"
        //getExternalFilesDir(Environment.DIRECTORY_MOVIES)
        val file = File(tempVideoFile, fileName)
        val outputOptions = FileOutputOptions.Builder(file).build()
        val currentRecording =
            videoCapture.output.prepareRecording(applicationContext, outputOptions)
        recording =
            currentRecording.start(ContextCompat.getMainExecutor(applicationContext)) { recordEvent ->
                when (recordEvent) {
                    is VideoRecordEvent.Start -> {
                        Log.e("VideoCapture22", "录制开始")
                    }

                    is VideoRecordEvent.Finalize -> {
                        if (!recordEvent.hasError()) {
                            Log.e(
                                "VideoCapture22",
                                "录制完成: ${file.absolutePath}${file.length()}"
                            )
                            videoRecordingService?.addVideoList(file.absolutePath)
                        } else {
                            Log.e("VideoCapture22", "录制出错: ${recordEvent.error}")
                        }
                        // 录制完成后，重置 recording
                        recording = null
                        // 只在这里判断是否需要自动重录
                        if (identifyStatus == IdentifyStatus.STARTED) {
                            startRecording()
                        }
                    }
                }
            }

        // 新建一个 Runnable 并保存引用
        delayedStopTask = Runnable { recording?.stop() }
        mainHandler.postDelayed(delayedStopTask!!, 4000)
    }


    fun stopRecording() {
        delayedStopTask?.let { mainHandler.removeCallbacks(it) }
        delayedStopTask = null
        recording?.stop()
        recording = null
    }

    private fun onEndClick() {
        identifyStatus = IdentifyStatus.NOT_STARTED
        binding.startButtonText.text = "开始"
        binding.startButton.setImageResource(R.mipmap.carmer_play); // 更改为新的资源
        binding.endButton.visibility = View.GONE
        // viewModel.endCurrentSession()
        stopRecording()
        LogService.info("结束检测会话")
        // 结束相机活动并返回
        // 1. 发送关闭事件通知 Flutter
        plugin.didClosePage("channel_closed")
        // 2. 关闭当前 Activity
        finish()
    }

    private fun onShowClick() {
        binding.goalView.visibility = if (binding.goalView.visibility == View.VISIBLE) {
            View.GONE
        } else {
            View.VISIBLE
        }
    }

    private fun onToggleDetectionClick() {
        shouldDrawDetectionBoxes = !shouldDrawDetectionBoxes
//        binding.toggleDetectionButton.text =
//            if (shouldDrawDetectionBoxes) "隐藏检测框" else "显示检测框"
        if (!shouldDrawDetectionBoxes) {
            binding.overlay.clear()
        }
    }



    private fun onResumeClick() {
        onStartClick()
    }

    private fun onCloseGoalViewClick() {
        binding.goalView.visibility = View.GONE
    }
}
