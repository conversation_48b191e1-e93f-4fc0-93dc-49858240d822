import 'dart:developer';
import 'dart:io';

import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:utils_package/utils_package.dart';

/// 权限工具类
class WxPermissionUtils {
  /// 相册权限
  static Future<bool> photo({
    bool openAppSetting = false,
    Function? handle,
  }) async {
    Permission permission = Permission.photos;
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt <= 32) {
        permission = Permission.storage;
      }
    }
    if (Platform.isAndroid) {
      if (await permission.isDenied) {
        PermissionToastUtils.showPermissionToast(
            context: Get.context!,
            message: '我们需要访问您的相册，以便您可以选择照片作为您的个人头像，用于编辑个人资料');
      }
    }
    final status = await permission.request();
    if (status.isGranted || status.isLimited) {
      if (handle != null) {
        handle();
      }
      return true;
    } else {
      if (openAppSetting) {
        openAppSettings();
      }
      return false;
    }
  }

  /// 相册权限
  static Future<bool> photo2({
    bool openAppSetting = false,
    Function? handle,
  }) async {
    Permission permission = Permission.photos;
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt <= 32) {
        permission = Permission.storage;
      }
    }
    if (Platform.isAndroid) {
      if (await permission.isDenied) {
        PermissionToastUtils.showPermissionToast(
            context: Get.context!, message: '我们需要访问您的相册，以便您可以选择照片作为您的球队图标');
      }
    }
    final status = await permission.request();
    if (status.isGranted || status.isLimited) {
      if (handle != null) {
        handle();
      }
      return true;
    } else {
      if (openAppSetting) {
        openAppSettings();
      }
      return false;
    }
  }

  ///添加图片到相册权限 iOS only
  static Future<bool> addPhoto({
    bool openAppSetting = false,
    Function? handle,
  }) async {
    final status = await Permission.photosAddOnly.request();
    if (status.isGranted || status.isLimited) {
      if (handle != null) {
        handle();
      }
      return true;
    } else {
      if (openAppSetting) {
        openAppSettings();
      }
      return false;
    }
  }

  ///存储权限 不包括读取 读取权限小于等于28 READ_EXTERNAL_STORAGE
  static Future<bool> storage() async {
    if (Platform.isIOS) {
      return true;
    }
    if (Platform.isAndroid) {
      final info = await DeviceInfoPlugin().androidInfo;
      if (info.version.sdkInt > 28) {
        //无需权限即可添加视频到相册，存储内容到应用程序专用的外部存储目录
        return true;
      }
      if (Platform.isAndroid) {
        if (await Permission.storage.isDenied) {
          PermissionToastUtils.showPermissionToast(
              context: Get.context!,
              message: '我们需要在您的设备上保存视频，以便您可以在相册中查看和管理这些视频');
        }
      }
      final status = await Permission.storage.status;
      if (status == PermissionStatus.granted) {
        return true;
      }

      final result = await Permission.storage.request();
      return result == PermissionStatus.granted;
    }

    throw StateError('unknown platform');
  }

  /// 相机权限
  static Future<bool> camera({
    bool openAppSetting = true,
    Function? handle,
  }) async {
    final status = await Permission.camera.request();
    if (status.isGranted) {
      if (handle != null) {
        handle();
      }
      return true;
    } else {
      if (openAppSetting) {
        openAppSettings();
      }
      return false;
    }
  }

  /// 麦克风权限 + 相机权限
  static Future<void> microphoneAndCamera({
    bool openAppSetting = true,
    Function? handle,
  }) async {
    var request = await [Permission.microphone, Permission.camera].request();
    var microphone = request[Permission.microphone];
    var camera = request[Permission.camera];
    if (microphone!.isGranted && camera!.isGranted) {
      if (handle != null) {
        handle();
      }
    } else {
      if (openAppSetting) {
        openAppSettings();
      }
    }
  }

  /// 麦克风权限 + 相机权限 存储
  static Future<void> microphoneAndCameraStorage({
    bool openAppSetting = true,
    Function? handle,
  }) async {
    var request = await [
      //Permission.microphone,
      Permission.camera,
      Permission.storage
    ].request();
    if (Platform.isAndroid) {
      if (await Permission.storage.isDenied) {
        PermissionToastUtils.showPermissionToast(
            context: Get.context!,
            message: '我们需要在您的设备上保存视频、图片，以便您可以在相册中查看和管理这些视频');
      }
    }
    //  var microphone = request[Permission.microphone];
    var camera = request[Permission.camera];
    var storage = request[Permission.storage];
    log("camera!.isGranted=${camera!.isGranted}  storage!.isGranted=${storage}"); // && storage!.isGranted
    if (camera!.isGranted) {
      if (handle != null) {
        handle();
      }
    } else {
      if (openAppSetting) {
        openAppSettings();
      }
    }
  }

  /// 麦克风权限
  static Future<void> microphone({
    bool openAppSetting = true,
    Function? handle,
  }) async {
    final status = await Permission.microphone.request();
    if (status.isGranted) {
      if (handle != null) {
        handle();
      }
    } else {
      if (openAppSetting) {
        openAppSettings();
      }
    }
  }

  /// 健康权限
  static Future<void> health({
    bool openAppSetting = true,
    Function? handle,
  }) async {
    final status = await Permission.activityRecognition.request();
    if (status.isGranted) {
      if (handle != null) {
        handle();
      }
    } else {
      if (openAppSetting) {
        openAppSettings();
      }
    }
  }

  /// 定位权限
  /// 仅在定位权限被拒绝时，才会弹出权限申请框
  static Future<void> location({
    bool openAppSetting = true,
    Function? handle,
  }) async {
    final status = await Permission.location.request();
    if (status.isGranted) {
      if (handle != null) {
        handle();
      }
    } else {
      if (openAppSetting) {
        openAppSettings();
      }
    }
  }
}
