// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

class Colours {
  static const Color white = Color(0xFFFFFFFF);
  static const Color app_main = Color(0xFF9A46FF);
  static const Color dark_app_main = Color(0xFF3F7AE0);

  static const Color bg_color = Color(0xff0F0F16);
  static const Color dark_bg_color = Color(0xFF18191A);
  static const Color c10ffffff = Color(0x10FFFFFF);
  static const Color material_bg = Color(0xFFFFFFFF);
  static const Color dark_material_bg = Color(0xFF303233);

  static const Color text = Color(0xFFFFFFFF);
  static const Color dark_text = Color(0xFFB8B8B8);

  static const Color text_gray = Color(0x7300101f); //45%
  static const Color dark_text_gray = Color(0xFF666666);

  static const Color text_gray_c = Color(0xFFcccccc);
  static const Color dark_button_text = Color(0xFFF2F2F2);

  static const Color bg_gray = Color(0xFFF6F6F6);
  static const Color dark_bg_gray = Color(0xFF1F1F1F);

  static const Color line = Color(0xFFEEEEEE);
  static const Color dark_line = Color(0xFF3A3C3D);

  static const Color red = Color(0xFFEE5152);
  static const Color dark_red = Color(0xFFE03E4E);

  static const Color text_disabled = Color(0xFFD4E2FA);
  static const Color dark_text_disabled = Color(0xFFCEDBF2);

  static const Color button_disabled = Color(0xFFBEEBD4);
  static const Color dark_button_disabled = Color(0xFF83A5E0);

  static const Color unselected_item_color = Color(0xffbfbfbf);
  static const Color dark_unselected_item_color = Color(0xFF4D4D4D);

  static const Color bg_gray_ = Color(0xFFFAFAFA);
  static const Color dark_bg_gray_ = Color(0xFF242526);

  static const Color gradient_blue = Color(0xFF5793FA);
  static const Color shadow_blue = Color(0x805793FA);
  static const Color orange = Color(0xFFFAAD14);
  static const Color div = Color(0xFFE9EcEE);
  static const Color e1e8e8 = Color(0xFFe1e8e8);
  static const Color e1e8eb = Color(0xFFe1e8eb);

  /// Color: #00101F
  static const Color color00101F = Color(0xFF00101F);

  /// Color: #165DFF
  static const Color color165DFF = Color(0xFF165DFF);

  /// Color: #4C3C3D
  static const Color color4C3C3D = Color(0xFF4C3C3D);

  /// Color: #9AA3F4
  static const Color color9AA3F4 = Color(0xFF9AA3F4);

  /// Color: #C9CEFA
  static const Color colorC9CEFA = Color(0xFFC9CEFA);

  /// Color: #8995C7
  static const Color color8995C7 = Color(0xFF8995C7);
  static const Color color217732ED = Color(0x217732ED);
  static const Color color890AFF = Color(0xFF890AFF);
  static const Color colorFFF9DC = Color(0xFFFFF9DC);
  static const Color colorA9A9A9 = Color(0xFFA9A9A9);
  static const Color colorDDDFE2 = Color(0xFFDDDFE2);
  static const Color color5C5C6E = Color(0xFF5C5C6E);
  static const Color color4C311F = Color(0xFF4C311F);
  static const Color color161212 = Color(0xFF161212);
  static const Color colorFFCC00 = Color(0xFFFFCC00);
  static const Color color191921 = Color(0xFF191921);
  static const Color color191919 = Color(0xFF191919);
  static const Color color242424 = Color(0xFF242424);
  static const Color colorE4C8FF = Color(0xFFE4C8FF);
  static const Color colorE5F3FF = Color(0xFFE5F3FF);
  static const Color color7732ED = Color(0xFF7732ED);
  static const Color color884C0B = Color(0xFF884C0B);
  static const Color colorD15B1B = Color(0xFFD15B1B);
  static const Color color3A3A42 = Color(0xFF3A3A42);
  static const Color color99292937 = Color(0x99292937);
  static const Color colorffFFBB68 = Color(0xffFFBB68);
  static const Color colorffFFEE9C = Color(0xffFFEE9C);
  static const Color colorffF8DB9C = Color(0xffF8DB9C);
  static const Color colorffFFCF51 = Color(0xffFFCF51);
  static const Color color922BFF = Color(0xFF922BFF);
  static const Color color1AFFFFFF = Color(0x1AFFFFFF);
  static const Color colorA555EF = Color(0xFFA555EF);
  static const Color color7C4CCE = Color(0xFF7C4CCE);
  static const Color color191523 = Color(0xFF191523);
  static const Color color288200FB = Color(0x288200FB);
  static const Color color03D8AFFF = Color(0x03D8AFFF);
  static const Color color2C2C39 = Color(0xFF2C2C39);
  static const Color color767681 = Color(0xFF767681);
  static const Color color0E1017 = Color(0xFF0E1017);
  static const Color color9D4FEF = Color(0xFF9D4FEF);
  static const Color color333333 = Color(0xFF333333);
  static const Color color000000 = Color(0xFF000000);
  static const Color colorFF661A = Color(0xFFFF661A);
  static const Color colorFF3F3F = Color(0xFFFF3F3F);
  static const Color color2A2A32 = Color(0xFF2A2A32);
  static const Color color262626 = Color(0xFF262626);
  static const Color colorEDDBFF = Color(0xFFEDDBFF);
  static const Color color666666 = Color(0xFF666666);
  static const Color colorEBEBEB = Color(0xFFEBEBEB);
  static const Color color999999 = Color(0xFF999999);
  static const Color color1AD8D8D8 = Color(0x1AD8D8D8);
  static const Color color2F2F3B = Color(0xFF2F2F3B);
  static const Color colorA44EFF = Color(0xFFA44EFF);
  static const Color color282735 = Color(0xFF282735);
  static const Color color353542 = Color(0xFF353542);
  static const Color color6F6F84 = Color(0xFF6F6F84);
  static const Color colorFFD60A = Color(0xFFFFD60A);
  static const Color color0F0F16 = Color(0xff0F0F16);
  static const Color color1C1C24 = Color(0xff1C1C24);
  static const Color color1C1827 = Color(0xff1C1827);
  static const Color color9393A5 = Color(0xff9393A5);
  static const Color color5D5D6E = Color(0xff5D5D6E);
  static const Color color1F1F29 = Color(0xff1F1F29);
  static const Color color1F1C22 = Color(0xff1F1C22);
  static const Color colorE9E9E9 = Color(0xffE9E9E9);
  static const Color color9E9E9E = Color(0xff9E9E9E);
  static const Color colorFFD574 = Color(0xFFFFD574);
  static const Color color22222D = Color(0xFF22222D);
  static const Color color964AEE = Color(0xFF964AEE);
  static const Color color291A3B = Color(0xFF291A3B);
  static const Color colorD8D8D8 = Color(0xFFD8D8D8);
  static const Color colorD9D9D9 = Color(0xFFD9D9D9);
  static const Color color10D8D8D8 = Color(0x10D8D8D8);
  static const Color color80000000 = Color(0x80000000);
  static const Color color50000000 = Color(0x50000000);
  static const Color color70000000 = Color(0x70000000);
  static const Color color9A46FF = Color(0xFF9A46FF);
  static const Color color813AEE = Color(0xFF813AEE);
  static const Color color9045EE = Color(0xFF9045EE);
  static const Color color7654A2 = Color(0xFF7654A2);
  static const Color color7F38ED = Color(0xFF7F38ED);
  static const Color colorFFE562 = Color(0xFFFFE562);
  static const Color colorA54040 = Color(0xFFA54040);
  static const Color colorC396F5 = Color(0xffC396F5);
  static const Color colorD5B6F8 = Color(0xffD5B6F8);
  static const Color color282835 = Color(0xFF282835);
  static const Color colorE282FF = Color(0xFFE282FF);
  static const Color color3B5844 = Color(0xFF3B5844);
  static const Color color5E2E33 = Color(0xFF5E2E33);
  static const Color color800F0F16 = Color(0x800F0F16);
  static const Color color252530 = Color(0xFF252530);
  static const Color color15151D = Color(0xFF15151D);
  static const Color colorFFF280 = Color(0xFFFFF280);
  static const Color color474CA4 = Color(0xFF474CA4);
  static const Color color513663 = Color(0xFF513663);
  static const Color color505583 = Color(0xFF505583);
  static const Color color432B8A = Color(0xFF432B8A);
  static const Color colorFCFCFC = Color(0xFFFCFCFC);
  static const Color color752A0073 = Color(0x752A0073);
  static const Color color9E6EFF = Color(0xFF9E6EFF);
  static const Color colorC3ABFF = Color(0xFFC3ABFF);
  static const Color colorD6D6D6 = Color(0xFFD6D6D6);
  static const Color colorBFFF9C = Color(0xFFBFFF9C);
  static const Color colorEEFC62 = Color(0xFFEEFC62);
  static const Color colorD2ABFF = Color(0xFFD2ABFF);
  static const Color colorCDFDDE = Color(0xFFCDFDDE);
  static const Color colorC5FE94 = Color(0xFFC5FE94);
  static const Color colorF3FC5C = Color(0xFFF3FC5C);
  static const Color colorFFECC1 = Color(0xFFFFECC1);

  static const Color colorE7CEFF = Color(0xFFE7CEFF);
  static const Color colorD1EAFF = Color(0xFFD1EAFF);
  static const Color color1E1E1E = Color(0xFF1E1E1E);
  static const Color color7B35ED = Color(0xFF7B35ED);
  static const Color colorA253EF = Color(0xFFA253EF);
  static const Color colorB88DFF = Color(0xFFB88DFF);
  static const Color colorEA431A = Color(0xFFEA431A);
  static const Color colorFFFC4E = Color(0xFFFFFC4E);
  static const Color colorBBFFA0 = Color(0xFFBBFFA0);
  static const Color color7F21E2 = Color(0xFF7F21E2);
  static const Color color2E1575 = Color(0xFF2E1575);
  static const Color color6435E9 = Color(0xFF6435E9);
  static const Color color665C6E = Color(0xFF665C6E);
  static const Color colorA8A8BC = Color(0xFFA8A8BC);
  static const Color color0DB600 = Color(0xFF0DB600);
  static const Color color6234E3 = Color(0xFF6234E3);
  static const Color color33FFFFFF = Color(0x33FFFFFF);
}
